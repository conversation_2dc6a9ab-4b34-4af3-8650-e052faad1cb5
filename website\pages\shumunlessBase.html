<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>数投无人基 - 数投科技</title>
    <meta http-equiv="keywords" content="数投无人基,智能投资管理,AI投资,数投科技,量化投资" />
    <meta http-equiv="description" content="数投无人基是基于AI技术的智能投资管理产品，通过先进的算法和大数据分析，为投资者提供专业的资产配置和投资管理服务。" />
    <meta name="Resource-type" content="Document" />

    <link rel="stylesheet" type="text/css" href="../style/coumon.css" />
    <style>
        body {
            font-family: "Microsoft YaHei", sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }

        .hero-section {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 120px 0;
            text-align: center;
        }

        .hero-content h1 {
            font-size: 56px;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .hero-content p {
            font-size: 24px;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .hero-features {
            display: flex;
            justify-content: center;
            gap: 60px;
            margin-top: 60px;
        }

        .hero-feature {
            text-align: center;
        }

        .hero-feature h3 {
            font-size: 32px;
            margin-bottom: 10px;
            color: #3498db;
        }

        .hero-feature p {
            font-size: 16px;
            opacity: 0.8;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .section {
            padding: 80px 0;
        }

        .section-title {
            text-align: center;
            font-size: 36px;
            color: #333;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .section-subtitle {
            text-align: center;
            font-size: 18px;
            color: #666;
            margin-bottom: 60px;
            line-height: 1.6;
        }

        .features-section {
            background: white;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }

        .feature-card {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
            border: 1px solid #eee;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 32px;
            color: white;
        }

        .feature-card h3 {
            font-size: 24px;
            color: #333;
            margin-bottom: 15px;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
            font-size: 16px;
        }

        .tech-section {
            background: #f8f9fa;
        }

        .tech-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 60px;
            align-items: center;
        }

        .tech-content h2 {
            font-size: 32px;
            color: #333;
            margin-bottom: 30px;
        }

        .tech-list {
            list-style: none;
            padding: 0;
        }

        .tech-list li {
            padding: 15px 0;
            border-bottom: 1px solid #eee;
            color: #666;
            font-size: 16px;
        }

        .tech-list li:last-child {
            border-bottom: none;
        }

        .tech-list li strong {
            color: #333;
        }

        .tech-visual {
            text-align: center;
        }

        .tech-diagram {
            width: 100%;
            max-width: 500px;
            height: 400px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin: 0 auto;
        }

        .benefits-section {
            background: white;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 40px;
        }

        .benefit-item {
            text-align: center;
            padding: 30px;
        }

        .benefit-number {
            font-size: 48px;
            color: #3498db;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .benefit-item h3 {
            font-size: 20px;
            color: #333;
            margin-bottom: 15px;
        }

        .benefit-item p {
            color: #666;
            line-height: 1.5;
        }

        .cta-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            text-align: center;
        }

        .cta-container {
            background: white;
            border-radius: 20px;
            padding: 60px 40px;
            display: inline-block;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .cta-container h2 {
            font-size: 32px;
            color: #333;
            margin-bottom: 20px;
        }

        .cta-container p {
            font-size: 18px;
            color: #666;
            margin-bottom: 40px;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 15px 40px;
            border-radius: 50px;
            text-decoration: none;
            font-size: 18px;
            font-weight: bold;
            transition: transform 0.3s ease;
        }

        .cta-button:hover {
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .hero-content h1 {
                font-size: 42px;
            }
            .hero-content p {
                font-size: 18px;
            }
            .hero-features {
                flex-direction: column;
                gap: 30px;
            }
            .tech-grid {
                grid-template-columns: 1fr;
                gap: 40px;
            }
            .benefits-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div class="header">
        <ul>
            <li>
                <a href="../index.html">首页</a>
            </li>
            <li>
                <a href="aboutYushi.html">关于我们</a>
            </li>
            <li>
                <a>专业机构服务</a>
                <div class="second-level">
                    <a href="javascript:void(0)" id="toKunpeng">鲲鹏财富管理</a>
                    <a href="AItouYan.html">AI投研</a>
                    <a href="kunpengAssetAllocation.html">鲲鹏资产配置助手</a>
                    <a href="shumunlessBase.html">数投无人基</a>
                    <a href="javascript:void(0)" id="toljs">炼金术系统</a>
                </div>
            </li>
            <li class="middle-logo">
                <a href="../index.html"></a>
            </li>
            <li>
                <a href="touLaboratory.html">智能投顾实验室</a>
            </li>
            <li>
                <a href="inforYushi.html">公司动态</a>
            </li>
            <li>
                <a href="joinUs.html">加入我们</a>
            </li>
        </ul>
    </div>

    <div class="hero-section">
        <div class="container">
            <div class="hero-content">
                <h1 style="font-size: 54px; font-weight: bold; margin-bottom: 18px; color: #fff; text-shadow: 2px 2px 8px #0E2553, 0 0 12px #000;">理财总踩坑？数投为您护航</h1>
                <p style="font-size: 28px; color: #FFD700; margin-bottom: 30px;">享30天体验权🔥</p>
                <div style="margin-bottom:32px;">
                    
                </div>
                <div style="font-size: 22px; color: #fff; margin-bottom: 40px;">五大权益护航</div>
                <div class="features-grid" style="margin-bottom: 40px;">
                    <div class="feature-card">
                        <h3>热点产品分析</h3>
                        <p>每日热点产品分析，教你聪明选基</p>
                    </div>
                    <div class="feature-card">
                        <h3>产品实力对比</h3>
                        <p>全方位对比分析，优劣一目了然</p>
                    </div>
                    <div class="feature-card">
                        <h3>产品智能体检</h3>
                        <p>一键体检持仓健康度，市场异动即时预警</p>
                    </div>
                    <div class="feature-card">
                        <h3>投资百科课堂</h3>
                        <p>每日5分钟，投资知识轻松get</p>
                    </div>
                    <div class="feature-card">
                        <h3>专属报告解读</h3>
                        <p>银行经理1v1报告解读，定制财富方案</p>
                    </div>
                </div>
                <div style="margin: 30px 0 40px 0;">
                    <span style="font-size: 20px; color: #fff; background: #3498db; border-radius: 8px; padding: 10px 24px;">现在开通，立享1个月体验</span>
                </div>
                <div style="margin-bottom: 30px;">
                    <img src="../images/wrjqr.jpg" alt="权益领取二维码" style="width:160px; height:160px; border-radius:16px; box-shadow:0 2px 12px rgba(0,0,0,0.12);">
                </div>
            </div>
        </div>
    </div>

    <div class="section" style="background:#fff;">
        <div class="container">
            <div class="section-title" style="font-size:28px; color:#0E2553;">为什么您需要这项服务？</div>
            <div style="display:flex; flex-wrap:wrap; justify-content:center; gap:40px; margin:30px 0 20px 0;">
                <div style="background:#f8d7da; color:#721c24; border-radius:10px; padding:18px 32px; font-size:18px; min-width:260px;">❌ 海量产品无从下手？</div>
                <div style="background:#f8d7da; color:#721c24; border-radius:10px; padding:18px 32px; font-size:18px; min-width:260px;">❌ 看不懂晦涩的产品说明？</div>
                <div style="background:#f8d7da; color:#721c24; border-radius:10px; padding:18px 32px; font-size:18px; min-width:260px;">❌ 担心隐藏风险？</div>
            </div>
            <div style="margin: 30px 0 10px 0; text-align:center; font-size:20px; color:#333;">我们帮您解决：</div>
            <div style="display:flex; flex-wrap:wrap; justify-content:center; gap:40px;">
                <div style="background:#d4edda; color:#155724; border-radius:10px; padding:18px 32px; font-size:18px; min-width:260px;">✅ 深度剖析每只产品的收益来源和风险点</div>
                <div style="background:#d4edda; color:#155724; border-radius:10px; padding:18px 32px; font-size:18px; min-width:260px;">✅ 用通俗语言讲透复杂的金融逻辑</div>
            </div>
        </div>
    </div>

    <div class="section" style="background:#f8f9fa;">
        <div class="container">
            <div class="section-title" style="font-size:28px; color:#0E2553;">核心功能亮点</div>
            <ul style="font-size:18px; color:#333; line-height:2.2; max-width:700px; margin:0 auto 30px auto;">
                <li>• 每日更新，紧跟市场动态</li>
                <li>• 专业团队+AI算法双重把关</li>
            </ul>
        </div>
    </div>

    <div class="section" style="background:#fff;">
        <div class="container">
            <div class="section-title" style="font-size:28px; color:#0E2553;">深度产品解析</div>
            <div style="display:flex; flex-wrap:wrap; align-items:center; gap:40px; justify-content:center;">
                <img src="../images/wrj.png" alt="产品解析示意图" style="max-width:340px; border-radius:16px; box-shadow:0 2px 12px rgba(0,0,0,0.12);">
                <ul style="font-size:18px; color:#333; line-height:2.2; min-width:320px;">
                    <li>✓ 收益构成可视化分析</li>
                    <li>✓ 风险指标白话解读</li>
                    <li>✓ 基金经理风格画像</li>
                    <li>✓ 历史表现多维度评估</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section" style="background:#f8f9fa;">
        <div class="container">
            <div class="section-title" style="font-size:28px; color:#0E2553;">实时异动监控</div>
            <ul style="font-size:18px; color:#333; line-height:2.2; max-width:700px; margin:0 auto 30px auto;">
                <li>• 7×24小时AI盯盘</li>
                <li>• 重大波动即时提醒</li>
                <li>• 持仓产品健康度评分</li>
            </ul>
        </div>
    </div>

    <div class="section" style="background:#fff;">
        <div class="container">
            <div class="section-title" style="font-size:28px; color:#0E2553;">服务价值</div>
            <ul style="font-size:18px; color:#333; line-height:2.2; max-width:700px; margin:0 auto 30px auto;">
                <li>• 每天5分钟掌握优质投资机会</li>
                <li>• 理财新手边投资边学习专业知识</li>
                <li>• 完善投资决策体系</li>
            </ul>
        </div>
    </div>

    <div class="section" style="background:linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);">
        <div class="container" style="text-align:center;">
            <div class="section-title" style="font-size:28px; color:#B48B4E;">限时福利</div>
            <div style="font-size:20px; color:#333; margin-bottom:18px;">立即注册即享：1个月VIP体验权限</div>
            <div style="font-size:16px; color:#666; margin-bottom:18px;">已有8,742位用户开启智能投资</div>
            <img src="../images/wrjqr.jpg" alt="权益领取二维码" style="width:160px; height:160px; border-radius:16px; box-shadow:0 2px 12px rgba(0,0,0,0.12);">
        </div>
    </div>

</body>

</html>
