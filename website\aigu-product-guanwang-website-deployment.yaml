apiVersion: apps/v1
kind: Deployment
metadata:
  name: aigu-guanwang-website-deployment
  namespace: aigu-product
  labels:
    web: aigu-guanwang-website-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: aigu-guanwang-website-deployment
  template:
    metadata:
      labels:
        app: aigu-guanwang-website-deployment
    spec:
#      nodeSelector:
#        env: aigu-product
      containers:
      - name: aigu-guanwang-website
        image: ${IMAGE_TAG}
        imagePullPolicy: Always
        ports:
        - containerPort: 80
        env:
          - name: SPRING_PROFILES_ACTIVE
            value: "product"      
          - name: time
            value: "1"
        volumeMounts:
          - name: nas-aigu-product-website-springboot-pvc
            mountPath: "/usr/share/nginx/html/aiguplus/download"
      imagePullSecrets:          
      - name: myregistrykey            

      volumes:
      - name: nas-aigu-product-website-springboot-pvc
        persistentVolumeClaim:
          claimName: nas-aigu-product-website-springboot-pvc

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: nas-aigu-product-website-springboot-pvc
  namespace: aigu-product
  annotations:
    volume.beta.kubernetes.io/storage-class: "alicloud-nas-product"
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: alicloud-nas-product
  resources:
    requests:
      storage: 5Gi

---
apiVersion: v1
kind: Service
metadata:
  name: aigu-guanwang-website
  namespace: aigu-product
spec:
  type: NodePort
  ports:
  - port: 80
    targetPort: 80
  selector:
    app: aigu-guanwang-website-deployment

---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: website
  namespace: aigu-product
spec:
  tls:
  - hosts:
    - www.51lianjin.com
    secretName: aigu-secret
  rules:
  - host: www.51lianjin.com
    http:
      paths:
      - backend:
          serviceName: aigu-guanwang-website
          servicePort: 80
        path: /