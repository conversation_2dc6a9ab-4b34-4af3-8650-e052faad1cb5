@charset "UTF-8";

body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
form,
fieldset,
input,
textarea,
p,
blockquote,
th,
td {
    padding: 0;
    margin: 0;
}

a {
    text-decoration: none;
}

a:hover,
a:link,
a:VISITED,
a:active {
    color: black;
    text-decoration: none;
}

table {
    border-spacing: 0;
}

fieldset,
img {
    border: 0;
}

address,
caption,
cite,
code,
dfn,
em,
strong,
th,
var {
    font-weight: normal;
    font-style: normal;
}

strong {
    font-weight: bold;
}

ol,
ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

caption,
th {
    text-align: left;

}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: normal;
    font-size: 100%;
    margin: 0;
    padding: 0;
    color: #444;
}

q:before,
q:after {
    content: '';
}

abbr,
acronym {
    border: 0;
}


/* Custom CSS
 * --------------------------------------- */
body {
    font-family: arial, helvetica;
    color: #333;
    color: rgba(0, 0, 0, 0.5);
}

.wrap {
    margin-left: auto;
    margin-right: auto;
    width: 960px;
    position: relative;
}

h1 {
    font-size: 6em;
}

p {
    font-size: 2em;
}

/*头部导航*/
/**头部导航 start**/
.header {
    position: absolute;
    width: 100%;
    min-width: 1170px;
    height: 100px;
    background-color: #0E2553;
}

.header ul {
    display: flex;
    max-width: 1170px;
    justify-content: space-between;
    position: absolute;
    top: 34px;
    left: 0px;
    right: 0px;
    margin: 0 auto;
}

.header ul .middle-logo {
    min-width: 180px;
    position: relative;
}

.header ul .middle-logo a {
    position: absolute;
    top: -30px;
    left: 0px;
    right: 0px;
    display: block;
    width: 100% !important;
    height: 80px;
    background-image: url(../images/Bitmap.png);
    background-size: 146px 60px;
    background-position: center;
    background-repeat: no-repeat;
}

.header ul li {
    position: relative;
}

.header ul li:hover .second-level {
    opacity: 1;
    visibility: visible;
}

.header ul li>a {
    color: #fff;
    font-size: 18px;
    padding: 20px 0px;
    width: 140px;
    text-align: center;
    box-sizing: border-box;
    display: block;
    font-weight: 500;
    transition: color 0.3s ease;
}

.header ul li>a:hover {
    color: #B48B4E;
}

.header ul li .second-level {
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s;
    position: absolute;
    left: -10px;
    right: -10px;
    top: 64px;
}

.header ul li .second-level:before {
    content: '';
    display: block;
    height: 2px;
    width: 80px;
    background-color: #887047;
    position: absolute;
    top: 0px;
    left: 50%;
    transform: translateX(-50%);
}

.header ul li .second-level a {
    background-color: #0E2553;
    color: #fff;
    display: block;
    font-size: 16px;
    padding: 15px 10px;
    border-bottom: 1px solid #585c5f;
    text-align: center;
    font-weight: 400;
}

.header ul li .second-level a:hover {
    color: #B48B4E;
    background-color: rgba(180, 139, 78, 0.1);
}

.header ul li .second-level a:nth-last-child(1) {
    border-bottom: none;
}

/*标题*/
.titles {
    margin: 0 auto;
    margin-top: 75px;
    font-size: 48px;
    color: #B48B4E;
    font-weight: 700;
    width: 600px;
    border-bottom: 1px solid #B48B4E;
    margin-bottom: 46px;
    margin-top: 40px;
    padding-bottom: 15px;
}

.concentRow {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: center;
}

.conBetwoon {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
}

.disNone {
    display: none !important;
}


/*底部*/
.footer {
    width: 100%;
    height: 100%;
    background: url(../images/footerBg.png) no-repeat;
    color: #fff;
    position: relative;
    background-size: cover;
    min-height: 750px;
    overflow-y: scroll;
}

.footerBox {
    width: 1120px;
    position: absolute;
    left: 50%;
    margin-left: -560px;
    bottom: 45px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
}

.footerBox div {
    padding: 0 40px;
}

.footOne {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 360px;
    border-right: 1px solid #fff;
}

.footerBox ul li {
    font-size: 12px;
    margin-bottom: 5px;
}

.footerBox ul li a:hover {
    color: #fff;
}

.footerBox ul li:first-child {
    font-size: 18px;
    margin-bottom: 15px;
}

.footTwo {
    /*width: 390px;*/
    padding-left: 60px;
    /* border-right: 1px solid #fff; */
}

.footTwo ul li {
    text-align: left;
}

.footTwo ul li:first-child a {
    padding-left: 25px;
}

.footTwo ul li img {
    width: 16px;
    height: 16px;
    /*display: inline-block;*/
    margin-right: 10px;
}

.footTwo ul li:nth-child(2) img {
    height: 20px;
}

.footThree {
    width: 90px;
}

.footThree .erNo {
    width: 90px;
}

.footThree .erNo img {
    width: 90px;
    height: 90px;
}

.erName {
    width: 90px;
    margin-top: 10px;
    font-size: 12px;
}


.corpyMsg {
    margin-top: 60px;
    font-size: 10px;
    font-weight: 200;
    color: rgba(255, 255, 255, .4);
}

.coverPage {
    position: fixed;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, .3);
    z-index: 9999;
    display: none;
}

.coverContent {
    position: absolute;
    top: 50%;
    left: 50%;
    background-color: #fff;
    z-index: 9999;
}

.close {
    font-size: 30px;
    /*width: 100%;*/
    text-align: right;
    padding: 15px 20px 1px;
}

.coverBtn {
    margin: 0 auto;
    width: 216px;

}

.coverBtn a {
    width: 216px;
    height: 66px;
    background-color: #CBAE60;
    color: #fff;
    font-size: 22px;
    line-height: 66px;
    display: block;
    text-align: center;
    border-radius: 32px;
}