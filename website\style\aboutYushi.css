.section {
    text-align: center;
}


/* Backgrounds will cover all the section
* --------------------------------------- */
.section {
    background-size: cover;
}

.slide {
    background-size: cover;
}

#toljs {
    font-size: 14px;
}

.firstSesion {
    padding-top: 100px;
    width: 100%;
    background-image: url(../images/Group11.png);
    /*background-size: 100% 100%;*/
    background-size: cover;
}

.checkMenu {
    position: absolute;
    top: 0;
    width: 100%;
    background-color: #fff;
    z-index: 9999;
    font-size: 20px;
    color: #000;
}

.checkMenu ul {
    width: 1150px;
    margin: 40px auto;
}

.checkMenu ul li {
    border-left: 1px solid #CDCDCD;
}

.checkMenu ul li:first-child {
    border: none;
}

.checkMenu ul li a {
    width: 190px;
    display: block;
}

.checkMenu ul .active {
    color: #BC9761;
}


.concentRow {
    padding-top: 70px;
    width: 1150px;
    justify-content: space-between;
    margin: 0 auto;
}

.concentRow .left {
    /* 使用Flexbox布局 */
    display: flex;
    /* 垂直居中 */
    align-items: center;
    width: 349px;
}

.concentRow .left img {
    width: 349px;
    height: 314px;
}

.concentRow .right {
    /* 使用Flexbox布局 */
    display: flex;
    /* 垂直居中 */
    align-items: center;
    width: 700px;
    margin-left: 50px;
}

.concentRow .right .ritghtCon {
    font-size: 16px;
    color: rgba(0, 0, 0, .7);
    word-break: break-all;
    text-align: left;
    line-height: 26px;
}

.concentRow .pattern {
    position: relative;
    margin-bottom: 80px;
}

.concentRow .pattern img {
    width: 500px;
    height: 493px;
}

.concentRow .pattern span {
    position: absolute;
    top: 355px;
    font-size: 16px;
    word-break: break-all;
    width: 420px;
    left: 46px;
    text-align: left;
    color: #BC9761;
    line-height: 26px;
}

.concentRow .pattern b {
    position: absolute;
    top: 150px;
    left: 0;
    font-size: 26px;
    width: 500px;
    text-align: center;
    color: #fff;
    font-weight: 600;
}

.conpanDes {
    width: 1149px;
    margin: 0 auto;
}

.conpanDes ul {}

.conpanDes ul li {
    width: 1149px;
    height: 148px;
    margin-bottom: 34px;
    position: relative;
}

.conpanDes ul li:nth-child(2n-1) {
    background: url(../images/Group1113.png) no-repeat;
}

.conpanDes ul li:nth-child(2n) {
    background: url(../images/Group151.png) no-repeat;
}

.conpanDes ul li div {
    position: absolute;
}

.conpanDes ul li .name {
    width: 148px;
    height: 150px;
    color: #fff;
    line-height: 150px;
    font-size: 22px;
}

.conpanDes ul li .masg {
    font-size: 18px;
    line-height: 150px;
    width: 904px;
    height: 150px;
    padding: 0 48px;
}

.conpanDes ul li:nth-child(2n-1) .name {
    left: 0;
}

.conpanDes ul li:nth-child(2n) .name {
    right: 0;
}

.conpanDes ul li:nth-child(2n-1) .masg {
    right: 0;
    text-align: left;
}

.conpanDes ul li:nth-child(2n) .masg {
    left: 0;
    text-align: right;
}

.leaderMsg {
    width: 1150px;
    margin: 0 auto;
}

.leaderMsg ul li {
    position: relative;
    width: 1150px;
    height: 300px;
    margin-bottom: 80px;
}

.leaderMsg ul li .pic {
    position: absolute;
}

.leaderMsg ul li .pic img {
    width: 200px;
    height: 300px;
}

.leaderMsg ul li .masg {
    width: 900px;
    height: 300px;
    background: url(../images/Group10.png) no-repeat;
    position: absolute;
    -webkit-background-size: 900px 300px;
    background-size: 900px 300px;
}

.leaderMsg ul li .name {
    position: absolute;
    top: 0;
    left: 0;
    width: 175px;
    height: 75px;
    color: #fff;
    line-height: 75px;
    font-size: 26px;
}

.leaderMsg ul li .learName {
    position: absolute;
    top: 0;
    left: 175px;
    height: 75px;
    line-height: 75px;
    font-size: 22px;
    text-align: left;
    padding-left: 30px;
}

.leaderMsg ul li .learDes {
    position: absolute;
    top: 75px;
    left: 0;
    font-size: 18px;
    word-break: break-all;
    width: 820px;
    padding: 33px 38px;
    text-align: left;
    line-height: 28px;
}

.bigThing {
    position: relative;
    width: 1150px;
    margin: 0 auto;
}

.bigThing ul {
    width: 830px;
    margin: 0 auto;
}

.bigThing ul li {
    width: 830px;
    height: 155px;
    display: flex;
    flex-wrap: nowrap;
    flex-direction: row;
    justify-content: center;
    margin-bottom: 60px;
}

.bigThing ul li .pic {
    margin: 0 30px;
    width: 380px;
}

.bigThing ul li .pic img {
    width: 230px;
    height: 153px;
    display: inline-block;
}

.bigThing ul li .masg {
    padding-top: 30px;
    margin: 0 30px;
    width: 380px;
}

.bigThing ul li .masg .name {
    font-size: 24px;
    color: #000;
}

.bigThing ul li .masg .learDes {
    font-size: 16px;
}

.bigThing ul li:nth-child(2n-1) .masg,
.bigThing ul li:nth-child(2n) .pic {
    text-align: left;
}

.bigThing ul li:nth-child(2n) .masg,
.bigThing ul li:nth-child(2n-1) .pic {
    text-align: right;
}

.timerLine {
    position: absolute;
    width: 16px;
    height: 896px;
    left: 50%;
    margin-left: -8px;
    top: 50px;
}

.honorMsg {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    width: 1140px;
    margin: 30px auto 100px;

}

.honorMsg .pattern {
    position: relative;
}

.honorMsg .pattern img {
    width: 280px;
    height: 286px;
}

.honorMsg .pattern span {
    position: absolute;
    top: 215px;
    font-size: 20px;
    word-break: break-all;
    width: 226px;
    left: 25px;
    color: #BC9761;

}

.titles {
    margin-top: 0;
    padding-top: 120px;
}