apiVersion: apps/v1
kind: Deployment
metadata:
  name: guanwang-website-deployment
  namespace: aigu
  labels:
    web: guanwang-website-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: guanwang-website-deployment
  template:
    metadata:
      labels:
        app: guanwang-website-deployment
    spec:
      containers:
      - name: guanwang-website
        image: ${IMAGE_TAG}
        imagePullPolicy: Always
        ports:
        - containerPort: 80
        env:
          - name: SPRING_PROFILES_ACTIVE
            value: "test"      
          - name: time
            value: "1"
        volumeMounts:
          - name: website-pvc
            mountPath: "/usr/share/nginx/html/aiguplus/download"
      imagePullSecrets:          
      - name: myregistrykey            

      volumes:
      - name: website-pvc
        persistentVolumeClaim:
          claimName: website-pvc

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: website-pvc
  namespace: aigu
  annotations:
    volume.beta.kubernetes.io/storage-class: "alicloud-nas"
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: alicloud-nas
  resources:
    requests:
      storage: 5Gi

---
apiVersion: v1
kind: Service
metadata:
  name: guanwang-website
  namespace: aigu
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 80
  selector:
    app: guanwang-website-deployment

---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: website
  namespace: aigu
spec:
  tls:
  - hosts:
    - test.51lianjin.com
    secretName: aigu-secret
  rules:
  - host: test.51lianjin.com
    http:
      paths:
      - backend:
          serviceName: guanwang-website
          servicePort: 80
        path: /