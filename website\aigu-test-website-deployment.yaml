apiVersion: v1
kind: Service
metadata:
  name: website-service
  namespace: test
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 80
  selector:
    app: website-deployment
  
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: website-deployment
  namespace: test
  labels:
    web: website-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: website-deployment
  template:
    metadata:
      labels:
        app: website-deployment
    spec:
      nodeSelector:
        env: test
      containers:
      - name: aigu-website
        image: registry.cn-shanghai.aliyuncs.com/jrljs/ai:aigu-website-${TAG}
        imagePullPolicy: Always
        ports:
        - containerPort: 80
        resources:            
          limits:
            cpu: "512m"
            memory: "1024Mi"
          requests:
            cpu: "256m"
            memory: "512Mi"  
        env:
          - name: SPRING_PROFILES_ACTIVE
            value: "test"      
          - name: time
            value: "1"
        volumeMounts:
          - name: nas-aigu-test-website-springboot-pvc
            mountPath: "/var/log/nginx"
      imagePullSecrets:          
      - name: myregistrykey            

      volumes:
      - name: nas-aigu-test-website-springboot-pvc
        persistentVolumeClaim:
          claimName: nas-aigu-test-website-springboot-pvc

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: nas-aigu-test-website-springboot-pvc
  namespace: aigu-test
  annotations:
    volume.beta.kubernetes.io/storage-class: "alicloud-nas-test"
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: alicloud-nas-test
  resources:
    requests:
      storage: 5Gi