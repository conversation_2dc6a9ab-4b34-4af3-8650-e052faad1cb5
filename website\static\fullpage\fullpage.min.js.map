{"version": 3, "sources": ["fullpage.js"], "names": ["root", "window", "document", "factory", "undefined", "define", "amd", "fullpage", "exports", "module", "this", "WRAPPER", "WRAPPER_SEL", "RESPONSIVE", "NO_TRANSITION", "DESTROYED", "ENABLED", "VIEWING_PREFIX", "ACTIVE", "ACTIVE_SEL", "COMPLETELY", "SECTION", "SECTION_SEL", "SECTION_ACTIVE_SEL", "TABLE_CELL", "TABLE_CELL_SEL", "AUTO_HEIGHT", "NORMAL_SCROLL", "SECTION_NAV", "SECTION_NAV_SEL", "SECTION_NAV_TOOLTIP", "SLIDE", "SLIDE_SEL", "SLIDE_ACTIVE_SEL", "SLIDES_WRAPPER", "SLIDES_WRAPPER_SEL", "SLIDES_CONTAINER", "SLIDES_CONTAINER_SEL", "TABLE", "SLIDES_NAV", "SLIDES_NAV_SEL", "SLIDES_NAV_LINK_SEL", "SLIDES_ARROW", "SLIDES_ARROW_SEL", "SLIDES_PREV", "SLIDES_ARROW_PREV_SEL", "SLIDES_ARROW_NEXT_SEL", "showError", "type", "text", "console", "$", "selector", "context", "arguments", "length", "querySelectorAll", "deepExtend", "out", "i", "len", "obj", "key", "hasOwnProperty", "Object", "prototype", "toString", "call", "hasClass", "el", "className", "classList", "contains", "RegExp", "test", "getWindowHeight", "innerHeight", "documentElement", "offsetHeight", "getWindowWidth", "innerWidth", "css", "items", "props", "getList", "style", "until", "item", "fn", "sibling", "matches", "prevUntil", "nextUntil", "prev", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "last", "index", "isArrayOrList", "children", "parentNode", "childNodes", "num", "nodeType", "hide", "display", "show", "addClass", "add", "removeClass", "classNames", "split", "a", "remove", "replace", "join", "appendTo", "parent", "append<PERSON><PERSON><PERSON>", "wrap", "toWrap", "wrapper", "isWrapAll", "newParent", "createElement", "cloneNode", "insertBefore", "wrapAll", "wrapInner", "createElementFromHTML", "<PERSON><PERSON><PERSON><PERSON>", "unwrap", "wrapperContent", "createDocumentFragment", "<PERSON><PERSON><PERSON><PERSON>", "closest", "after", "reference", "nextS<PERSON>ling", "before", "beforeElement", "getScrollTop", "doc", "pageYOffset", "scrollTop", "clientTop", "siblings", "Array", "filter", "child", "preventDefault", "event", "returnValue", "isFunction", "trigger", "eventName", "data", "CustomEvent", "detail", "createEvent", "initCustomEvent", "dispatchEvent", "matchesSelector", "msMatchesSelector", "mozMatchesSelector", "webkitMatchesSelector", "oMatchesSelector", "toggle", "value", "htmlString", "div", "innerHTML", "trim", "parentElement", "<PERSON><PERSON><PERSON><PERSON>", "untilAll", "push", "nextAll", "prevAll", "NodeList", "for<PERSON>ach", "callback", "thisArg", "fp_utils", "filterFn", "containerSelector", "options", "isOK", "domain", "indexOf", "$htmlBody", "$html", "$body", "FP", "menu", "anchors", "lockAnchors", "navigation", "navigationPosition", "navigationTooltips", "showActiveTooltip", "slidesNavigation", "slidesNavPosition", "scrollBar", "hybrid", "css3", "scrollingSpeed", "autoScrolling", "fitToSection", "fitToSectionDelay", "easing", "easingcss3", "loopBottom", "loopTop", "loopHorizontal", "continuousVertical", "continuousHorizontal", "scrollHorizontally", "interlockedSlides", "dragAndMove", "offsetSections", "resetSliders", "fadingEffect", "normalScrollElements", "scrollOverflow", "scrollOverflowReset", "scrollOverflowHandler", "fp_scrolloverflow", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "scrollOverflowOptions", "touchSensitivity", "touchWrapper", "bigSectionsDestination", "keyboardScrolling", "animateAnchor", "recordHistory", "controlArrows", "controlArrowColor", "verticalCentered", "sectionsColor", "paddingTop", "paddingBottom", "fixedElements", "responsive", "responsiveWidth", "responsiveHeight", "responsiveSlides", "parallax", "parallaxOptions", "percentage", "property", "cards", "cardsOptions", "perspective", "fadeContent", "fadeBackground", "sectionSelector", "slideSelector", "v2compatible", "afterLoad", "onLeave", "afterRender", "afterResize", "afterReBuild", "afterSlideLoad", "onSlideLeave", "afterResponsive", "lazyLoading", "lastScrolledDestiny", "lastScrolledSlide", "controlPressed", "startingSection", "slideMoving", "isTouchDevice", "navigator", "userAgent", "match", "is<PERSON><PERSON>ch", "msMaxTouchPoints", "container", "windowsHeight", "windowsWidth", "isResizing", "isWindowFocused", "canScroll", "scrollings", "isScrollAllowed", "m", "up", "down", "left", "right", "k", "scrollBarHandler", "resizeId", "resizeHandlerId", "afterSectionLoadsId", "afterSlideLoadsId", "scrollId", "scrollId2", "keydownId", "g_doubleCheckHeightId", "MSPointer", "PointerEvent", "move", "events", "touchmove", "touchstart", "focusableElementsString", "g_supportsPassive", "opts", "defineProperty", "get", "addEventListener", "removeEventListener", "e", "activeAnimation", "g_mediaLoadedId", "originals", "g_initialAnchorsInDom", "g_canFireMouseEnterNormalScroll", "extensions", "displayWarnings", "fp_easings", "easeInOutCubic", "t", "b", "c", "d", "version", "setAutoScrolling", "setRecordHistory", "setScrollingSpeed", "setFitToSection", "setLockAnchors", "setMouseWheelScrolling", "setAllowScrolling", "setKeyboardScrolling", "moveSectionUp", "moveSectionDown", "silentMoveTo", "moveTo", "moveSlideRight", "moveSlideLeft", "reBuild", "setResponsive", "getFullpageData", "destroy", "all", "setMouseHijack", "timeoutId", "clearTimeout", "<PERSON><PERSON><PERSON><PERSON>", "hash<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resize<PERSON><PERSON>ler", "keydownHandler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delegatedEvents", "onMouseEnterOrLeave", "silentScroll", "setSrc", "height", "background-color", "padding", "width", "position", "-ms-touch-action", "touch-action", "overflow", "previousStyles", "getAttribute", "setAttribute", "removeAttribute", "removeAnimation", "-webkit-transition", "transition", "scrollTo", "getActiveSection", "Section", "getActiveSlide", "nullOrSlide", "top", "translate3d", "translate3dH", "shared", "afterRenderActions", "isNormalScrollElement", "fullpage_api", "keys", "has3d", "transforms", "webkitTransform", "OTransform", "msTransform", "MozTransform", "transform", "body", "getComputedStyle", "getPropertyValue", "support3d", "attrName", "tooltips", "setOptionsFromDOM", "sections", "sectionIndex", "section", "slides", "numSlides", "padding-top", "padding-bottom", "activateMenuAndNav", "styleSlides", "addTableClass", "divUl", "nav", "li", "link", "getBulletLinkName", "tooltip", "margin-top", "addVerticalNavigation", "element", "newParam", "originalSrc", "init", "prepareDom", "setBodyClass", "readyState", "scrollToAnchor", "setTimeout", "adjustToNewViewport", "doubleCheckHeight", "<PERSON><PERSON><PERSON><PERSON>", "forMouseLeaveOrTouch", "isScrolling", "lastScroll", "touchStartY", "touchStartX", "touchEndY", "touchEndX", "prevTime", "Date", "getTime", "previousDestTop", "oldPageY", "previousHeight", "setVariableState", "offsetTop", "scrollSettings", "getScrollSettings", "_addEventListener", "prefix", "support", "onmousew<PERSON><PERSON>", "passiveEvent", "passive", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addMouseWheelHandler", "mouseDownHandler", "mouseUpHandler", "detachEvent", "directions", "direction", "setIsScrollAllowed", "preventBouncing", "touchStartHandler", "touchMoveHandler", "addTouchHandler", "remove<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scrollPage", "sectionAnchor", "slideAnchor", "destiny", "getSectionByAnchor", "scrollPageAndSlide", "moveSlide", "resizing", "slidesWrap", "getTableHeight", "landscapeScroll", "createScrollBarForAll", "isResponsiveMode", "active", "isResponsive", "target", "indexBullet", "allowScrolling", "toElement", "relatedTarget", "isInsideOneNormalScroll", "normalSelector", "isNormalScrollTarget", "isNormalScrollChildFocused", "newWindowHeight", "newWindowWidth", "slider<PERSON><PERSON><PERSON>", "slideWidth", "slidesWrapper", "arrows", "slidesContainer", "border-color", "margin-left", "addSlidesNavigation", "slide", "startingSlide", "silentLandscapeScroll", "defaultName", "anchor", "destinationSection", "lazyLoad", "lazyLoadOthers", "playMedia", "getAnchorsURL", "fireCallback", "activeSection", "anchorLink", "currentSection", "movement", "bottom", "currentScroll", "scrollDirection", "visibleSectionIndex", "screen_mid", "isAtBottom", "slideIndex", "slideAnchorLink", "leavingSection", "leavingSectionIndex", "yMovement", "getYmovement", "activeSlide", "callbacksParams", "stopMedia", "setState", "scrolling", "scrollSection", "scrollable", "check", "isScrolled", "is<PERSON><PERSON>ly<PERSON><PERSON><PERSON>", "touchEvents", "getEventsPage", "y", "x", "Math", "abs", "pointerType", "getAverage", "elements", "number", "sum", "lastElements", "slice", "max", "ceil", "curTime", "isNormalScroll", "wheelDelta", "deltaY", "delta", "min", "horizontalDetection", "wheelDeltaX", "deltaX", "isScrollingVertically", "shift", "timeDiff", "averageEnd", "currentSlide", "slideSiblings", "isTesting", "keepSlidesPosition", "activeSlides", "isMovementUp", "elementHeight", "elementTop", "isScrollingDown", "sectionBottom", "v", "dtop", "localIsResizing", "reverse", "wrapAroundElements", "beforeLeave", "round", "transformContainer", "afterSectionLoads", "performMovement", "objectData", "paramsPerEvent", "eventData", "prevSlide", "prevSlideIndex", "nullOrSection", "origin", "destination", "apply", "map", "Slide", "scroll", "attribute", "hasAutoHeightSections", "rect", "getBoundingClientRect", "getSlideOrSection", "onMediaLoad", "elementToPlay", "load", "onloadeddata", "createScrollBar", "panel", "hasAttribute", "play", "playYoutube", "onload", "contentWindow", "postMessage", "pause", "isFirstSlideMove", "isFirstScrollMove", "hash", "location", "anchorsParts", "isFunkyAnchor", "decodeURIComponent", "activeElement", "keyCode", "allFocusables", "currentFocusIndex", "focusDestinationIndex", "focusDestination", "destinationItemSlide", "destinationItemSection", "isShiftPressed", "shift<PERSON>ey", "focusableElements", "getFocusables", "preventAndFocusFirst", "focus", "onTab", "ctrl<PERSON>ey", "shiftPressed", "isMediaFocused", "onkeydown", "which", "pageY", "mouseMoveHandler", "offsetParent", "fromIndex", "toIndex", "destinyPos", "offsetLeft", "slidesNav", "getAnchor", "xMovement", "addAnimation", "getTransforms", "afterSlideLoads", "performHorizontalMove", "resizeActions", "currentHeight", "widthLimit", "heightLimit", "isBreakingPointWidth", "isBreakingPointHeight", "name", "sectionHeight", "paddings", "parseInt", "animated", "scrollSlider", "sectionHash", "setUrlHash", "url", "history", "replaceState", "baseUrl", "href", "elementIndex", "String", "classRe", "pageX", "touches", "noCallbacks", "roundedTop", "setScrolling", "-webkit-transform", "-moz-transform", "-ms-transform", "variable", "l", "msgStyle", "warn", "extension", "nameAttr", "toLowerCase", "idAttr", "propertyName", "to", "duration", "start", "self", "scrollLeft", "change", "currentTime", "animateScroll", "val", "<PERSON><PERSON>", "isLast", "<PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "extend"], "mappings": ";;;;;;;;;;CAUC,SAAUA,EAAMC,EAAQC,EAAUC,EAASC,GAClB,mBAAXC,QAAyBA,OAAOC,IAEvCD,OAAQ,WAEJ,OADAL,EAAKO,SAAWJ,EAAQF,EAAQC,GACzBF,EAAKO,WAEU,iBAAZC,QAEdC,OAAOD,QAAUL,EAAQF,EAAQC,GAGjCD,EAAOM,SAAWJ,EAAQF,EAAQC,GAZ1C,CAcEQ,KAAMT,OAAQC,SAAU,SAASD,GAAQC,IACvC,aAGA,IAAIS,GAAwB,mBACxBC,GAAwB,IAAMD,GAO9BE,GAAwB,gBACxBC,GAAwB,kBACxBC,GAAwB,eACxBC,GAAwB,aACxBC,GAAwB,aACxBC,GAAwB,SACxBC,GAAwB,IAAMD,GAC9BE,GAAwB,gBAKxBC,GAAwB,aACxBC,GAAwB,IAAMD,GAC9BE,GAAwBD,GAAcH,GACtCK,GAAwB,eACxBC,GAAwB,IAAMD,GAC9BE,GAAwB,iBAIxBC,GAAwB,mBAIxBC,GAAwB,SACxBC,GAAwB,IAAMD,GAC9BE,GAAwB,aAMxBC,GAAwB,WACxBC,GAAwB,IAAMD,GAC9BE,GAAwBD,GAAYb,GACpCe,GAAwB,YACxBC,GAAwB,IAAMD,GAC9BE,GAAwB,qBACxBC,GAAwB,IAAMD,GAC9BE,GAAwB,WAGxBC,GAAwB,eACxBC,GAAwB,IAAMD,GAC9BE,GAAwBD,GAAiB,KACzCE,EAAwB,kBACxBC,GAAwB,IAAMD,EAC9BE,GAAwB,UAGxBC,GAAwBF,GAFA,WAMxBG,GAAwBH,GAFA,WAi7G5B,SAASI,GAAUC,EAAMC,GACrBhD,GAAOiD,SAAWjD,GAAOiD,QAAQF,IAAS/C,GAAOiD,QAAQF,GAAM,aAAeC,GAMlF,SAASE,GAAEC,EAAUC,GAEjB,OADAA,EAA6B,EAAnBC,UAAUC,OAAaF,EAAUnD,IAC1BmD,EAAQG,iBAAiBJ,GAAY,KAM1D,SAASK,GAAWC,GAChBA,EAAMA,GAAO,GACb,IAAK,IAAIC,EAAI,EAAGC,EAAMN,UAAUC,OAAQI,EAAIC,IAAOD,EAAE,CACjD,IAAIE,EAAMP,UAAUK,GAEpB,GAAIE,EAIJ,IAAI,IAAIC,KAAOD,EACRA,EAAIE,eAAeD,KAKyB,oBAA7CE,OAAOC,UAAUC,SAASC,KAAKN,EAAIC,IAKvCJ,EAAII,GAAOD,EAAIC,GAJbJ,EAAII,GAAOL,GAAWC,EAAII,GAAMD,EAAIC,KAO5C,OAAOJ,EAMX,SAASU,GAASC,EAAIC,GAClB,OAAS,MAAND,IAGCA,EAAGE,UACIF,EAAGE,UAAUC,SAASF,GAE1B,IAAIG,OAAO,QAAUH,EAAY,QAAS,MAAMI,KAAKL,EAAGC,YAMnE,SAASK,KACL,MAAO,gBAAiB1E,GAASA,GAAO2E,YAAc1E,GAAS2E,gBAAgBC,aAMnF,SAASC,KACL,OAAO9E,GAAO+E,WAQlB,SAASC,GAAIC,EAAOC,GAGhB,IAAIrB,EACJ,IAAKA,KAHLoB,EAAQE,EAAQF,GAGJC,EACR,GAAIA,EAAMpB,eAAeD,IACT,OAARA,EACA,IAAK,IAAIH,EAAI,EAAGA,EAAIuB,EAAM3B,OAAQI,IAAK,CACxBuB,EAAMvB,GACZ0B,MAAMvB,GAAOqB,EAAMrB,GAMxC,OAAOoB,EAMX,SAASI,EAAMC,EAAMnC,EAAUoC,GAE3B,IADA,IAAIC,EAAUF,EAAKC,GACbC,IAAYC,GAAQD,EAASrC,IAC/BqC,EAAUA,EAAQD,GAGtB,OAAOC,EAMX,SAASE,GAAUJ,EAAMnC,GACrB,OAAOkC,EAAMC,EAAMnC,EAAU,0BAMjC,SAASwC,GAAUL,EAAMnC,GACrB,OAAOkC,EAAMC,EAAMnC,EAAU,sBAMjC,SAASyC,GAAKN,GACV,OAAOA,EAAKO,uBAMhB,SAASC,GAAKR,GACV,OAAOA,EAAKS,mBAMhB,SAASC,GAAKV,GACV,OAAOA,EAAKA,EAAKhC,OAAO,GAO5B,SAAS2C,GAAMX,EAAMnC,GACjBmC,EAAOY,EAAcZ,GAAQA,EAAK,GAAKA,EAGvC,IAFA,IAAIa,EAAuB,MAAZhD,EAAkBD,GAAEC,EAAUmC,EAAKc,YAAcd,EAAKc,WAAWC,WAC5EC,EAAM,EACD5C,EAAE,EAAGA,EAAEyC,EAAS7C,OAAQI,IAAK,CACjC,GAAIyC,EAASzC,IAAM4B,EAAM,OAAOgB,EACN,GAAtBH,EAASzC,GAAG6C,UAAaD,IAElC,OAAQ,EAMZ,SAASnB,EAAQG,GACb,OAAQY,EAAcZ,GAAiBA,EAAT,CAACA,GAMnC,SAASkB,GAAKpC,GACVA,EAAKe,EAAQf,GAEb,IAAI,IAAIV,EAAI,EAAGA,EAAEU,EAAGd,OAAQI,IACxBU,EAAGV,GAAG0B,MAAMqB,QAAU,OAE1B,OAAOrC,EAMX,SAASsC,GAAKtC,GACVA,EAAKe,EAAQf,GAEb,IAAI,IAAIV,EAAI,EAAGA,EAAEU,EAAGd,OAAQI,IACxBU,EAAGV,GAAG0B,MAAMqB,QAAU,QAE1B,OAAOrC,EAMX,SAAS8B,EAAc9B,GACnB,MAAgD,mBAAzCL,OAAOC,UAAUC,SAASC,KAAME,IACM,sBAAzCL,OAAOC,UAAUC,SAASC,KAAME,GAMxC,SAASuC,GAASvC,EAAIC,GAClBD,EAAKe,EAAQf,GAEb,IAAI,IAAIV,EAAI,EAAGA,EAAEU,EAAGd,OAAQI,IAAI,CAC5B,IAAI4B,EAAOlB,EAAGV,GACV4B,EAAKhB,UACLgB,EAAKhB,UAAUsC,IAAIvC,GAGrBiB,EAAKjB,WAAa,IAAMA,EAG9B,OAAOD,EAOX,SAASyC,GAAYzC,EAAIC,GACrBD,EAAKe,EAAQf,GAIb,IAFA,IAAI0C,EAAazC,EAAU0C,MAAM,KAEzBC,EAAI,EAAGA,EAAEF,EAAWxD,OAAQ0D,IAAI,CACpC3C,EAAYyC,EAAWE,GACvB,IAAI,IAAItD,EAAI,EAAGA,EAAEU,EAAGd,OAAQI,IAAI,CAC5B,IAAI4B,EAAOlB,EAAGV,GACV4B,EAAKhB,UACLgB,EAAKhB,UAAU2C,OAAO5C,GAGtBiB,EAAKjB,UAAYiB,EAAKjB,UAAU6C,QAAQ,IAAI1C,OAAO,UAAYH,EAAU0C,MAAM,KAAKI,KAAK,KAAO,UAAW,MAAO,MAI9H,OAAO/C,EAMX,SAASgD,GAAShD,EAAIiD,GAClBA,EAAOC,YAAYlD,GAavB,SAASmD,EAAKC,EAAQC,EAASC,GAC3B,IAAIC,EACJF,EAAUA,GAAWxH,GAAS2H,cAAc,OAC5C,IAAI,IAAIlE,EAAI,EAAGA,EAAI8D,EAAOlE,OAAQI,IAAI,CAClC,IAAI4B,EAAOkC,EAAO9D,IACfgE,IAAchE,IAAMgE,KACnBC,EAAYF,EAAQI,WAAU,GAC9BvC,EAAKc,WAAW0B,aAAaH,EAAWrC,IAE5CqC,EAAUL,YAAYhC,GAE1B,OAAOkC,EAYX,SAASO,GAAQP,EAAQC,GACrBF,EAAKC,EAAQC,GAAS,GAY1B,SAASO,GAAUX,EAAQI,GAOvB,IANuB,iBAAZA,IACPA,EAAUQ,GAAsBR,IAGpCJ,EAAOC,YAAYG,GAEbJ,EAAOa,aAAeT,GACxBA,EAAQH,YAAYD,EAAOa,YAYnC,SAASC,GAAOV,GAEZ,IADA,IAAIW,EAAiBnI,GAASoI,yBACvBZ,EAAQS,YACXE,EAAed,YAAYG,EAAQS,YAGvCT,EAAQrB,WAAWkC,aAAaF,EAAgBX,GAOpD,SAASc,GAAQnE,EAAIjB,GACjB,OAAGiB,GAAsB,IAAhBA,EAAGmC,SACLd,GAAQrB,EAAIjB,GACJiB,EAEJmE,GAAQnE,EAAGgC,WAAYjD,GAE3B,KASX,SAASqF,GAAMC,EAAWrE,GACtB0D,EAAaW,EAAWA,EAAUC,YAAatE,GASnD,SAASuE,GAAOF,EAAWrE,GACvB0D,EAAaW,EAAWA,EAAWrE,GAOvC,SAAS0D,EAAaW,EAAWG,EAAexE,GACxC8B,EAAc9B,KACE,iBAANA,IACNA,EAAK6D,GAAsB7D,IAE/BA,EAAK,CAACA,IAGV,IAAI,IAAIV,EAAI,EAAGA,EAAEU,EAAGd,OAAQI,IACxB+E,EAAUrC,WAAW0B,aAAa1D,EAAGV,GAAIkF,GAKjD,SAASC,KACL,IAAIC,EAAM7I,GAAS2E,gBACnB,OAAQ5E,GAAO+I,aAAeD,EAAIE,YAAeF,EAAIG,WAAa,GAMtE,SAASC,GAAS9E,GACd,OAAO+E,MAAMnF,UAAUoF,OAAOlF,KAAKE,EAAGgC,WAAWD,SAAU,SAASkD,GAClE,OAAOA,IAAUjF,IAKvB,SAASkF,GAAeC,GACjBA,EAAMD,eACLC,EAAMD,iBAGNC,EAAMC,aAAc,EAO5B,SAASC,GAAWnE,GAClB,GAAoB,mBAATA,EACT,OAAO,EAET,IAAIvC,EAAOgB,OAAOC,UAAUC,SAASqB,GACrC,MAAgB,sBAATvC,GAAyC,+BAATA,EAMzC,SAAS2G,GAAQtF,EAAIuF,EAAWC,GAC5B,IAAIL,EACJK,OAAuB,IAATA,EAAuB,GAAKA,EAGT,mBAAvB5J,GAAO6J,YACbN,EAAQ,IAAIM,YAAYF,EAAW,CAACG,OAAQF,KAG5CL,EAAQtJ,GAAS8J,YAAY,gBACvBC,gBAAgBL,GAAW,GAAM,EAAMC,GAGjDxF,EAAG6F,cAAcV,GAMrB,SAAS9D,GAAQrB,EAAIjB,GACjB,OAAQiB,EAAGqB,SAAWrB,EAAG8F,iBAAmB9F,EAAG+F,mBAAqB/F,EAAGgG,oBAAsBhG,EAAGiG,uBAAyBjG,EAAGkG,kBAAkBpG,KAAKE,EAAIjB,GAM3J,SAASoH,GAAOnG,EAAIoG,GAChB,GAAoB,kBAAVA,EACN,IAAI,IAAI9G,EAAI,EAAGA,EAAEU,EAAGd,OAAQI,IACxBU,EAAGV,GAAG0B,MAAMqB,QAAU+D,EAAQ,QAAU,OAKhD,OAAOpG,EAOX,SAAS6D,GAAsBwC,GAC3B,IAAIC,EAAMzK,GAAS2H,cAAc,OAIjC,OAHA8C,EAAIC,UAAYF,EAAWG,OAGpBF,EAAIxC,WAMf,SAASjB,GAAOhC,GACZA,EAAQE,EAAQF,GAChB,IAAI,IAAIvB,EAAI,EAAGA,EAAEuB,EAAM3B,OAAQI,IAAI,CAC/B,IAAI4B,EAAOL,EAAMvB,GACd4B,GAAQA,EAAKuF,eACZvF,EAAKc,WAAW0E,YAAYxF,IAaxC,SAASyF,EAASzF,EAAMnC,EAAUoC,GAG9B,IAFA,IAAIC,EAAUF,EAAKC,GACf2D,EAAW,GACT1D,IACCC,GAAQD,EAASrC,IAAyB,MAAZA,IAC7B+F,EAAS8B,KAAKxF,GAElBA,EAAUA,EAAQD,GAGtB,OAAO2D,EAMX,SAAS+B,GAAQ3F,EAAMnC,GACnB,OAAO4H,EAASzF,EAAMnC,EAAU,sBAMpC,SAAS+H,GAAQ5F,EAAMnC,GACnB,OAAO4H,EAASzF,EAAMnC,EAAU,0BAsEpC,OAtDInD,GAAOmL,WAAaA,SAASnH,UAAUoH,UACvCD,SAASnH,UAAUoH,QAAU,SAAUC,EAAUC,GAC7CA,EAAUA,GAAWtL,GACrB,IAAK,IAAI0D,EAAI,EAAGA,EAAIjD,KAAK6C,OAAQI,IAC7B2H,EAASnH,KAAKoH,EAAS7K,KAAKiD,GAAIA,EAAGjD,QAM/CT,GAAOuL,SAAW,CACdrI,EAAGA,GACHM,WAAYA,GACZW,SAAUA,GACVO,gBAAiBA,GACjBM,IAAKA,GACLK,MAAOA,EACPK,UAAWA,GACXC,UAAWA,GACXC,KAAMA,GACNE,KAAMA,GACNE,KAAMA,GACNC,MAAOA,GACPd,QAASA,EACTqB,KAAMA,GACNE,KAAMA,GACNR,cAAeA,EACfS,SAAUA,GACVE,YAAaA,GACbO,SAAUA,GACVG,KAAMA,EACNQ,QAASA,GACTC,UAAWA,GACXG,OAAQA,GACRI,QAASA,GACTC,MAAOA,GACPG,OAAQA,GACRb,aAAcA,EACde,aAAcA,GACdK,SAAUA,GACVI,eAAgBA,GAChBG,WAAYA,GACZC,QAASA,GACTjE,QAASA,GACT8E,OAAQA,GACRtC,sBAAuBA,GACvBhB,OAAQA,GACRmC,OA5FJ,SAAgBhF,EAAIoH,GAChBrC,MAAMnF,UAAUoF,OAAOlF,KAAKE,EAAIoH,IA4FhCT,SAAUA,EACVE,QAASA,GACTC,QAASA,GACTpI,UAAWA,IAp+Hf,SAAoB2I,EAAmBC,GACnC,IAAIC,EAAOD,GAAW,IAAIlH,OAAO,+FAA+FC,KAAKiH,EAAoC,cAAqE,EAA/DzL,GAAS2L,OAAOC,QAAQ,mBAGnMC,EAAY5I,GAAE,cACd6I,EAAQ7I,GAAE,QAAQ,GAClB8I,EAAQ9I,GAAE,QAAQ,GAGtB,IAAGiB,GAAS4H,EAAOhL,IAAnB,CAEA,IAAIkL,EAAK,GAGTP,EAAUlI,GAAW,CAEjB0I,MAAM,EACNC,QAAQ,GACRC,aAAa,EACbC,YAAY,EACZC,mBAAoB,QACpBC,mBAAoB,GACpBC,mBAAmB,EACnBC,kBAAkB,EAClBC,kBAAmB,SACnBC,WAAW,EACXC,QAAQ,EAGRC,MAAM,EACNC,eAAgB,IAChBC,eAAe,EACfC,cAAc,EACdC,kBAAmB,IACnBC,OAAQ,iBACRC,WAAY,OACZC,YAAY,EACZC,SAAS,EACTC,gBAAgB,EAChBC,oBAAoB,EACpBC,sBAAsB,EACtBC,oBAAoB,EACpBC,mBAAmB,EACnBC,aAAa,EACbC,gBAAgB,EAChBC,cAAc,EACdC,cAAc,EACdC,qBAAsB,KACtBC,gBAAgB,EAChBC,qBAAqB,EACrBC,sBAAuBlO,GAAOmO,kBAAoBnO,GAAOmO,kBAAkBC,eAAiB,KAC5FC,sBAAuB,KACvBC,iBAAkB,EAClBC,aAA2C,iBAAtB9C,EAAiCvI,GAAEuI,GAAmB,GAAKA,EAChF+C,uBAAwB,KAGxBC,mBAAmB,EACnBC,eAAe,EACfC,eAAe,EAGfC,eAAe,EACfC,kBAAmB,OACnBC,kBAAkB,EAClBC,cAAgB,GAChBC,WAAY,EACZC,cAAe,EACfC,cAAe,KACfC,WAAY,EACZC,gBAAiB,EACjBC,iBAAkB,EAClBC,kBAAkB,EAClBC,UAAU,EACVC,gBAAiB,CACbzM,KAAM,SACN0M,WAAY,GACZC,SAAU,aAEdC,OAAO,EACPC,aAAc,CACVC,YAAa,IACbC,aAAa,EACbC,gBAAgB,GAIpBC,gBArIoB,WAsIpBC,cAjHoB,SAoHpBC,cAAc,EACdC,UAAW,KACXC,QAAS,KACTC,YAAa,KACbC,YAAa,KACbC,aAAc,KACdC,eAAgB,KAChBC,aAAc,KACdC,gBAAiB,KAEjBC,aAAa,GACdjF,GAGH,IASIkF,EACAC,EAGAC,EACAC,EAdAC,GAAc,EAEdC,EAAgBC,UAAUC,UAAUC,MAAM,+GAC1CC,EAAY,iBAAkBrR,IAAyC,EAA7BkR,UAAUI,kBAA0BJ,UAAwB,eACtGK,EAAyC,iBAAtB9F,EAAiCvI,GAAEuI,GAAmB,GAAKA,EAC9E+F,EAAgB9M,KAChB+M,EAAe3M,KACf4M,GAAa,EACbC,GAAkB,EAGlBC,GAAY,EACZC,EAAa,GAGbC,EAAkB,CACtBC,EAAoB,CAAGC,IAAK,EAAMC,MAAO,EAAMC,MAAO,EAAMC,OAAQ,IACpEL,EAAgBM,EAAI5O,GAAW,GAAIsO,EAAgBC,GACnD,IAKIM,EAkBAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EA9BAC,EA02FG9S,GAAO+S,aACI,CAAEd,KAAM,cAAee,KAAM,eAK7B,CAAEf,KAAM,gBAAiBe,KAAM,iBA/2F7CC,EAAS,CACTC,UAAW,gBAAiBlT,GAAS,YAAe8S,EAAUE,KAC9DG,WAAY,iBAAkBnT,GAAS,aAAgB8S,EAAUb,MAKjEmB,EAA0B,iLAG1BC,GAAoB,EACxB,IACE,IAAIC,EAAOvP,OAAOwP,eAAe,GAAI,UAAW,CAC9CC,IAAK,WACHH,GAAoB,KAGxBrT,GAAOyT,iBAAiB,cAAe,KAAMH,GAC7CtT,GAAO0T,oBAAoB,cAAe,KAAMJ,GAChD,MAAOK,IAWT,IACIC,EAGAC,EAJAC,EAAYtQ,GAAW,GAAIkI,GAE3BqI,GAAwB,EACxBC,GAAkC,EAElCC,EAAa,CACb,WACA,sBACA,cACA,iBACA,eACA,mBACA,uBACA,oBACA,qBACA,eACA,SAGJC,KAGAlU,GAAOmU,WAAa3Q,GAAWxD,GAAOmU,WAAY,CAC9CC,eAAgB,SAAUC,EAAGC,EAAGC,EAAGC,GAC/B,OAAKH,GAAGG,EAAE,GAAK,EAAUD,EAAE,EAAEF,EAAEA,EAAEA,EAAIC,EAASC,EAAE,IAAIF,GAAG,GAAGA,EAAEA,EAAI,GAAKC,KA2T1E/C,IAECtF,EAAGwI,QAAU,QACbxI,EAAGyI,iBAAmBA,EACtBzI,EAAG0I,iBAAmBA,EACtB1I,EAAG2I,kBAAoBA,GACvB3I,EAAG4I,gBAAkBA,GACrB5I,EAAG6I,eA/OP,SAAwBtK,GACpBkB,EAAQU,YAAc5B,GA+OtByB,EAAG8I,uBAAyBA,GAC5B9I,EAAG+I,kBAAoBA,GACvB/I,EAAGgJ,qBAAuBA,GAC1BhJ,EAAGiJ,cAAgBA,GACnBjJ,EAAGkJ,gBAAkBA,GACrBlJ,EAAGmJ,aAAeA,GAClBnJ,EAAGoJ,OAASA,GACZpJ,EAAGqJ,eAAiBA,GACpBrJ,EAAGsJ,cAAgBA,GACnBtJ,EAAGe,aAAeA,GAClBf,EAAGuJ,QAAUA,GACbvJ,EAAGwJ,cAAgBA,GACnBxJ,EAAGyJ,gBAAkB,WAAY,OAAOhK,GACxCO,EAAG0J,QA8kFP,SAAiBC,GACblB,GAAiB,EAAO,YACxBM,IAAkB,GAClBa,IAAe,GACfZ,IAAqB,GACrBtO,GAAS4K,EAAWzQ,IAEpB,CACI2R,EACAD,EACAF,EACAI,EACAC,EACAE,EACAN,GACFnH,QAAQ,SAAS0K,GACfC,aAAaD,KAGjB9V,GAAO0T,oBAAoB,SAAUsC,IACrChW,GAAO0T,oBAAoB,aAAcuC,IACzCjW,GAAO0T,oBAAoB,SAAUwC,IAErCjW,GAASyT,oBAAoB,UAAWyC,IACxClW,GAASyT,oBAAoB,QAAS0C,IAEtC,CAAC,QAAS,cAAchL,QAAQ,SAASzB,GACrC1J,GAASyT,oBAAoB/J,EAAW0M,MAG5C,CAAC,aAAc,aAAc,aAAc,YAAYjL,QAAQ,SAASzB,GACpE1J,GAASyT,oBAAoB/J,EAAW2M,IAAqB,KAI9DV,IAUHW,GAAa,GAGbrT,GAAE,qEAAsEqO,GAAWnG,QAAQ,SAAS9F,GAChGkR,GAAOlR,EAAM,SAGjBpC,GAAE,oBAAoBkI,QAAQ,SAAS9F,GACnCkR,GAAOlR,EAAM,YAGjB2B,GAAO/D,GAAEtB,GAAkB,KAAOW,GAAkB,KAAOG,KAG3DsC,GAAI9B,GAAE7B,IAAc,CAChBoV,OAAU,GACVC,mBAAqB,GACrBC,QAAW,KAGf3R,GAAI9B,GAAEnB,IAAY,CACd6U,MAAS,KAGb5R,GAAIuM,EAAW,CACXkF,OAAU,GACVI,SAAY,GACZC,mBAAoB,GACpBC,eAAgB,KAGpB/R,GAAI8G,EAAW,CACXkL,SAAY,GACZP,OAAU,KAId5P,GAAYkF,EAAOhL,IAGnB8F,GAAYmF,EAAOpL,IAGnBoL,EAAM3H,UAAU0C,MAAM,OAAOqE,QAAQ,SAAU/G,GACD,IAAtCA,EAAUwH,QAAQ7K,KAClB6F,GAAYmF,EAAO3H,KAK3BnB,GAAE7B,GAAc,KAAOU,IAAWqJ,QAAQ,SAAS9F,GAC5CoG,EAAQwC,uBAAyBxC,EAAQsC,gBACxCtC,EAAQwC,sBAAsBjH,OAAO3B,GAEzCuB,GAAYvB,EAAMjD,GAAQ,IAAMpB,GAAS,IAAME,IAC/C,IAAI8V,EAAiB3R,EAAK4R,aAAa,kBACpCD,GACC3R,EAAK6R,aAAa,QAAS7R,EAAK4R,aAAa,mBAI9C/S,GAASmB,EAAMlE,MAAa2S,GAC3BzO,EAAK8R,gBAAgB,iBAK7BC,GAAgB9F,GAGhB,CAAC/P,GAAgBY,GAAqBF,IAAoBkJ,QAAQ,SAASjI,GACvED,GAAEC,EAAUoO,GAAWnG,QAAQ,SAAS9F,GAEpC6C,GAAO7C,OAKfN,GAAIuM,EAAW,CACX+F,qBAAsB,OACtBC,WAAc,SAIlBvX,GAAOwX,SAAS,EAAG,GAGC,CAACpW,GAASU,GAAOK,IACvBiJ,QAAQ,SAAS9F,GAC3BuB,GAAY3D,GAAE,IAAMoC,GAAOA,OAntF/B2G,EAAGwL,iBAs4FP,WACI,OAAO,IAAIC,GAAQxU,GAAE5B,IAAoB,KAt4FzC2K,EAAG0L,eA63FP,WAEI,OAAOC,GADW1U,GAAElB,GAAkBkB,GAAE5B,IAAoB,IAAI,KA53FhE2K,EAAGxH,KAAO,CACNoT,IAAK,MACLC,YAAa,6BACbC,aAAc,WAEV,IADA,IAAI/Q,EAAI,GACAtD,EAAI,EAAGA,EAAIR,GAAEwI,EAAQsE,gBAAiBuB,GAAWjO,OAAQI,IAC7DsD,EAAEgE,KAAK,8BAEX,OAAOhE,EALG,GAOdkL,KAAM,WAEF,IADA,IAAIlL,EAAI,GACAtD,EAAI,EAAGA,EAAIR,GAAEwI,EAAQsE,gBAAiBuB,GAAWjO,OAAQI,IAC7DsD,EAAEgE,KAAK,GAEX,OAAOhE,EALL,GAON0E,QAASA,EACTgJ,iBAAkBA,GAKtBzI,EAAG+L,OAAS,CACRC,mBAAoBA,GACpBC,uBAAuB,GAG3BlY,GAAOmY,aAAelM,EAGnBP,EAAQxI,GACPa,OAAOqU,KAAKnM,GAAIb,QAAQ,SAAUvH,GAC9B6H,EAAQxI,EAAEqC,GAAGjF,SAASuD,GAAOoI,EAAGpI,KAWrC6H,EAAQmB,OACPnB,EAAQmB,KA2yEhB,WACI,IACIwL,EADAjU,EAAKnE,GAAS2H,cAAc,KAE5B0Q,EAAa,CACTC,gBAAkB,oBAClBC,WAAa,eACbC,YAAc,gBACdC,aAAe,iBACfC,UAAY,aASpB,IAAK,IAAItE,KALTjQ,EAAGgB,MAAMqB,QAAU,QAGnBxG,GAAS2Y,KAAK9Q,aAAa1D,EAAI,MAEjBkU,OACUnY,IAAhBiE,EAAGgB,MAAMiP,KACTjQ,EAAGgB,MAAMiP,GAAK,2BACdgE,EAAQrY,GAAO6Y,iBAAiBzU,GAAI0U,iBAAiBR,EAAWjE,KAMxE,OAFApU,GAAS2Y,KAAK9N,YAAY1G,QAERjE,IAAVkY,GAAsC,EAAfA,EAAM/U,QAAwB,SAAV+U,EAr0EhCU,IAGnBrN,EAAQiB,UAAYjB,EAAQiB,WAAajB,EAAQkB,OA4KrD,WAGI,IAAIlB,EAAQS,QAAQ7I,OAAO,CACvB,IAAI0V,EAAW,gBACX7M,EAAUjJ,GAAEwI,EAAQsE,gBAAgBjJ,MAAM,KAAKI,KAAK6R,EAAW,KAAOA,EAAUzH,GACjFpF,EAAQ7I,SACPyQ,GAAwB,EACxB5H,EAAQf,QAAQ,SAAS9F,GACrBoG,EAAQS,QAAQnB,KAAK1F,EAAK4R,aAAa,eAAejT,eAMlE,IAAIyH,EAAQa,mBAAmBjJ,OAAO,CAClC,IAAI0V,EAAW,iBACXC,EAAW/V,GAAEwI,EAAQsE,gBAAgBjJ,MAAM,KAAKI,KAAK6R,EAAW,KAAOA,EAAUzH,GAClF0H,EAAS3V,QACR2V,EAAS7N,QAAQ,SAAS9F,GACtBoG,EAAQa,mBAAmBvB,KAAK1F,EAAK4R,aAAa,gBAAgBjT,eA9L9EiV,GAuMJ,WACIlU,GAAIuM,EAAW,CACXkF,OAAU,OACVI,SAAY,aAIhBlQ,GAAS4K,EAAW7Q,IACpBiG,GAASoF,EAAOhL,IAGhByQ,EAAgB9M,KAEhBmC,GAAY0K,EAAWzQ,IAmJvB6F,GAASzD,GAAEwI,EAAQsE,gBAAiBuB,GAAYnQ,IAChDuF,GAASzD,GAAEwI,EAAQuE,cAAesB,GAAYzP,IA7I9C,IAHA,IAAIqX,EAAWjW,GAAE7B,IAGTqC,EAAI,EAAGA,EAAEyV,EAAS7V,OAAQI,IAAI,CAClC,IAAI0V,EAAe1V,EACf2V,EAAUF,EAASzV,GACnB4V,EAASpW,GAAEnB,GAAWsX,GACtBE,EAAYD,EAAOhW,OAGvB+V,EAAQlC,aAAa,iBAAkBkC,EAAQnC,aAAa,UAmF9CmC,EAjFDA,GAiFUpT,EAjFDmT,IAmFe,MAA5BlW,GAAE5B,IAAoB,IAC/BqF,GAAS0S,EAASpY,IAEtB8P,EAAkB7N,GAAE5B,IAAoB,GAExC0D,GAAIqU,EAAS,CAAC5C,OAAUjF,EAAgB,OAErC9F,EAAQsD,YACPhK,GAAIqU,EAAS,CAACG,cAAe9N,EAAQsD,aAGtCtD,EAAQuD,eACPjK,GAAIqU,EAAS,CAACI,iBAAkB/N,EAAQuD,qBAGC,IAAlCvD,EAAQqD,cAAc9I,IAC7BjB,GAAIqU,EAAS,CAAC3C,mBAAoBhL,EAAQqD,cAAc9I,UAGtB,IAA3ByF,EAAQS,QAAQlG,IACvBoT,EAAQlC,aAAa,cAAezL,EAAQS,QAAQlG,IAOzCoT,EA7GDA,EA6GUpT,EA7GDmT,OA8Ge,IAA3B1N,EAAQS,QAAQlG,IAEpB9B,GAASkV,EAASpY,KACjByY,GAAmBhO,EAAQS,QAAQlG,GAAQA,GAKhDyF,EAAQQ,MAAQR,EAAQmB,MAAoD,MAA5CtE,GAAQrF,GAAEwI,EAAQQ,MAAM,GAAIvL,KAC3DuC,GAAEwI,EAAQQ,MAAMd,QAAQ,SAASc,GAC7BF,EAAM1E,YAAY4E,KArHN,EAAZqN,EACAI,GAAYN,EAASC,EAAQC,GAE1B7N,EAAQoD,kBACP8K,GAAcP,GAsG9B,IAAmBA,EAASpT,EA7BNoT,EAASpT,EAnExByF,EAAQwD,eAAiBxD,EAAQmB,MAChC3J,GAAEwI,EAAQwD,eAAe9D,QAAQ,SAAS9F,GACtC0G,EAAM1E,YAAYhC,KAKvBoG,EAAQW,YAsIf,WACI,IAAIA,EAAapM,GAAS2H,cAAc,OACxCyE,EAAW8K,aAAa,KAAMxV,IAE9B,IAAIkY,EAAQ5Z,GAAS2H,cAAc,MACnCyE,EAAW/E,YAAYuS,GAEvBzS,GAASiF,EAAYL,GACrB,IAAI8N,EAAM5W,GAAEtB,IAAiB,GAE7B+E,GAASmT,EAAK,MAAQpO,EAAQY,oBAE3BZ,EAAQc,mBACP7F,GAASmT,EAh+BO,kBAq+BpB,IAFA,IAAIC,EAAK,GAEArW,EAAI,EAAGA,EAAIR,GAAE7B,IAAaiC,OAAQI,IAAK,CAC5C,IAAIsW,EAAO,GACPtO,EAAQS,QAAQ7I,SAChB0W,EAAOtO,EAAQS,QAAQzI,IAG3BqW,GAAM,iBAAmBC,EAAO,8BAAgCC,GAAkBvW,EAAG,WAAa,2BAGlG,IAAIwW,EAAUxO,EAAQa,mBAAmB7I,QAElB,IAAZwW,GAAuC,KAAZA,IAClCH,GAAM,eAAiBlY,GAAsB,OAAS6J,EAAQY,mBAAqB,KAAO4N,EAAU,UAGxGH,GAAM,QAEV7W,GAAE,KAAM4W,GAAK,GAAGnP,UAAYoP,EAG5B/U,GAAI9B,GAAEtB,IAAkB,CAACuY,aAAc,IAAOjX,GAAEtB,IAAiB,GAAGiD,aAAa,EAAK,OAKtF8B,GAASzD,GAAE,IADEA,GAAE,KAAMA,GAAEtB,IAAiB,IAAIqE,GAAM/C,GAAE5B,IAAoB,GAAID,MACnDJ,IAhLrBmZ,GAgMJlX,GAAE,oCAAqCqO,GAAWnG,QAAQ,SAAS9F,GAQvE,IAAqB+U,EAASC,EACtBC,EADsBD,EAPJ,gBAQlBC,GADaF,EAPD/U,GAQU4R,aAAa,OACvCmD,EAAQlD,aAAa,MAAOoD,GASlB,KAAK9V,KAT2C8V,GASrB,IAAN,KAT0CD,KArMtE5O,EAAQsC,iBACPqE,EAAmB3G,EAAQwC,sBAAsBsM,KAAK9O,IA/P1D+O,GACAzF,IAAkB,GAClBa,IAAe,GACfnB,EAAiBhJ,EAAQqB,cAAe,YACxCoC,KAGAuL,KAE2B,aAAxBza,GAAS0a,YACRC,KAEJ5a,GAAOyT,iBAAiB,OAAQmH,IAG5BlP,EAAQsC,gBACRiK,KAkIR,WACI,IAAI,IAAIvU,EAAI,EAAGA,EAAI,EAAGA,IAClBmP,EAAwBgI,WAAWC,GAAqB,IAAMpX,GAjIlEqX,GAMA/a,GAAOyT,iBAAiB,SAAUuC,IAIlChW,GAAOyT,iBAAiB,aAAcwC,IAGtCjW,GAAOyT,iBAAiB,OAAQuH,IAGhChb,GAAOyT,iBAAiB,SAAUyC,IAGlCjW,GAASwT,iBAAiB,UAAW0C,IAGrClW,GAASwT,iBAAiB,QAAS2C,IAInC,CAAC,QAAS,cAAchL,QAAQ,SAASzB,GACrC1J,GAASwT,iBAAiB9J,EAAW0M,MAOtC3K,EAAQqC,uBACP,CAAC,aAAc,cAAc3C,QAAQ,SAASzB,GAC1CsR,GAAqBtR,GAAW,KAGpC,CAAC,aAAc,YAAYyB,QAAQ,SAASzB,GACzCsR,GAAqBtR,GAAW,OA8b3C,IAAIuR,GAAc,EACdC,EAAa,EAiObC,EAAc,EACdC,EAAc,EACdC,EAAY,EACZC,EAAY,EAuGZC,GAAW,IAAIC,MAAOC,UA2HtBC,EAAkB,EA60BlBC,EAAW,EAqJXC,EAAiBrK,EA28BrB,OAAOvF,EA5uGP,SAASyI,EAAiBlK,EAAOzH,GAEzByH,GACA+L,GAAa,GAGjBuF,GAAiB,gBAAiBtR,EAAOzH,GAEzC,IAAIsX,EAAUnX,GAAE5B,IAAoB,GAEpC,GAAGoK,EAAQqB,gBAAkBrB,EAAQiB,UACjC3H,GAAI8G,EAAW,CACXkL,SAAY,SACZP,OAAU,SAGd9B,EAAiBb,EAAUnF,cAAe,YAG1C3J,GAAIuM,EAAW,CACXuF,mBAAoB,OACpBC,eAAgB,SAGN,MAAXsD,GAEC9D,GAAa8D,EAAQ0B,gBAkBzB,GAfA/W,GAAI8G,EAAW,CACXkL,SAAa,UACbP,OAAW,YAIf9B,IADqBjJ,EAAQqB,eAAwB+G,EAAUnF,cAC/B,YAGhC3J,GAAIuM,EAAW,CACXuF,mBAAoB,GACpBC,eAAgB,KAIL,MAAXsD,EAAiB,CACjB,IAAI2B,EAAiBC,GAAkB5B,EAAQ0B,WAC/CC,EAAe3B,QAAQ7C,SAAS,EAAGwE,EAAetQ,UAQ9D,SAASiJ,EAAiBnK,EAAOzH,GAC7B+Y,GAAiB,gBAAiBtR,EAAOzH,GAM7C,SAAS6R,GAAkBpK,EAAOzH,GAC9B+Y,GAAiB,iBAAkBtR,EAAOzH,GAM9C,SAAS8R,GAAgBrK,EAAOzH,GAC5B+Y,GAAiB,eAAgBtR,EAAOzH,GAa5C,SAASgS,GAAuBvK,GACzBA,GAioFP,WACI,IACI0R,EADAC,EAAS,GAGTnc,GAAOyT,iBACPyI,EAAoB,oBAEpBA,EAAoB,cACpBC,EAAS,MAIb,IAAIC,EAAU,YAAanc,GAAS2H,cAAc,OAAS,aACvBzH,IAA1BF,GAASoc,aAA6B,aACtC,iBACNC,IAAejJ,GAAoB,CAACkJ,SAAS,GAEnC,kBAAXH,EACCnc,GAAUic,GAAoBC,EAAS,sBAAuBK,GAAmBF,GAKjFrc,GAAUic,GAAoBC,EAASC,EAASI,GAAmBF,GAvpFnEG,GA+pFJlL,EAAUkC,iBAAiB,YAAaiJ,IACxCnL,EAAUkC,iBAAiB,UAAWkJ,MA9ClC1c,GAASwT,kBACTxT,GAASyT,oBAAoB,aAAc8I,IAAmB,GAC9Dvc,GAASyT,oBAAoB,QAAS8I,IAAmB,GACzDvc,GAASyT,oBAAoB,sBAAuB8I,IAAmB,IAEvEvc,GAAS2c,YAAY,eAAgBJ,IAgDzCjL,EAAUmC,oBAAoB,YAAagJ,IAC3CnL,EAAUmC,oBAAoB,UAAWiJ,KA1pF7C,SAAS3H,GAAkBxK,EAAOqS,QACL,IAAfA,GACNA,EAAaA,EAAW3V,QAAQ,KAAK,IAAIH,MAAM,MAEpCqE,QAAQ,SAAU0R,GACzBC,GAAmBvS,EAAOsS,EAAW,OAIzCC,GAAmBvS,EAAO,MAAO,KAOzC,SAASqL,GAAerL,GACjBA,GACCuK,IAAuB,GA8oF/B,WACI,GAAG9D,GAAiBI,EAAQ,CACrB3F,EAAQqB,gBACPf,EAAM0H,oBAAoBT,EAAOC,UAAW8J,GAAiB,CAACT,SAAS,IACvEvQ,EAAMyH,iBAAiBR,EAAOC,UAAW8J,GAAiB,CAACT,SAAS,KAGxE,IAAIhO,EAAe7C,EAAQ6C,aAC3BA,EAAamF,oBAAoBT,EAAOE,WAAY8J,IACpD1O,EAAamF,oBAAoBT,EAAOC,UAAWgK,GAAkB,CAACX,SAAS,IAE/EhO,EAAakF,iBAAiBR,EAAOE,WAAY8J,IACjD1O,EAAakF,iBAAiBR,EAAOC,UAAWgK,GAAkB,CAACX,SAAS,KAzpF5EY,KAEApI,IAAuB,GA8pF/B,WACI,GAAG9D,GAAiBI,EAAQ,CAErB3F,EAAQqB,gBACPf,EAAM0H,oBAAoBT,EAAOC,UAAWgK,GAAkB,CAACX,SAAS,IACxEvQ,EAAM0H,oBAAoBT,EAAOC,UAAW8J,GAAiB,CAACT,SAAS,KAG3E,IAAIhO,EAAe7C,EAAQ6C,aAC3BA,EAAamF,oBAAoBT,EAAOE,WAAY8J,IACpD1O,EAAamF,oBAAoBT,EAAOC,UAAWgK,GAAkB,CAACX,SAAS,KAvqF/Ea,IAOR,SAASnI,GAAqBzK,EAAOqS,QACR,IAAfA,GACNA,EAAaA,EAAW3V,QAAQ,KAAK,IAAIH,MAAM,MAEpCqE,QAAQ,SAAS0R,GACxBC,GAAmBvS,EAAOsS,EAAW,QAGzCC,GAAmBvS,EAAO,MAAO,KACjCkB,EAAQ+C,kBAAoBjE,GAOpC,SAAS0K,KACL,IAAItP,EAAOF,GAAUxC,GAAE5B,IAAoB,GAAID,IAG1CuE,IAAS8F,EAAQ2B,UAAW3B,EAAQ6B,qBACrC3H,EAAOI,GAAK9C,GAAE7B,MAGN,MAARuE,GACAyX,GAAWzX,EAAM,MAAM,GAO/B,SAASuP,KACL,IAAIrP,EAAOH,GAAUzC,GAAE5B,IAAoB,GAAID,IAG3CyE,IACC4F,EAAQ0B,aAAc1B,EAAQ6B,qBAC/BzH,EAAO5C,GAAE7B,IAAa,IAGf,MAARyE,GACCuX,GAAWvX,EAAM,MAAM,GAQ/B,SAASsP,GAAakI,EAAeC,GACjC3I,GAAmB,EAAG,YACtBS,GAAOiI,EAAeC,GACtB3I,GAAmBd,EAAUhH,eAAgB,YAOjD,SAASuI,GAAOiI,EAAeC,GAC3B,IAAIC,EAAUC,GAAmBH,QAEN,IAAhBC,EACPG,GAAmBJ,EAAeC,GAClB,MAAXC,GACLH,GAAWG,GAQnB,SAASlI,GAAe+D,GACpBsE,GAAU,QAAStE,GAOvB,SAAS9D,GAAc8D,GACnBsE,GAAU,OAAQtE,GAMtB,SAAS7D,GAAQoI,GACb,IAAGzZ,GAASoN,EAAWzQ,IAAvB,CAEA4Q,GAAa,EAGbF,EAAgB9M,KAChB+M,EAAe3M,KAGf,IADA,IAAIqU,EAAWjW,GAAE7B,IACRqC,EAAI,EAAGA,EAAIyV,EAAS7V,SAAUI,EAAG,CACtC,IAAI2V,EAAUF,EAASzV,GACnBma,EAAa3a,GAAEhB,GAAoBmX,GAAS,GAC5CC,EAASpW,GAAEnB,GAAWsX,GAGvB3N,EAAQoD,kBACP9J,GAAI9B,GAAE1B,GAAgB6X,GAAU,CAAC5C,OAAUqH,GAAezE,GAAW,OAGzErU,GAAIqU,EAAS,CAAC5C,OAAUjF,EAAgB,OAGpB,EAAhB8H,EAAOhW,QACPya,GAAgBF,EAAY3a,GAAElB,GAAkB6b,GAAY,IAIjEnS,EAAQsC,gBACPqE,EAAiB2L,wBAGrB,IACI5E,EAAenT,GADC/C,GAAE5B,IAAoB,GACFD,IAGrC+X,GAEChE,GAAagE,EAAe,GAGhC1H,GAAa,EACVjI,GAAYiC,EAAQ4E,cAAiBsN,GACpClS,EAAQ4E,YAAYpM,KAAKqN,EAAWvR,GAAO+E,WAAY/E,GAAO2E,aAE/D8E,GAAYiC,EAAQ6E,gBAAmBqN,GACtClS,EAAQ6E,aAAarM,KAAKqN,IAOlC,SAAS0M,KACN,OAAO9Z,GAAS6H,EAAOpL,IAO1B,SAAS6U,GAAcyI,GACnB,IAAIC,EAAeF,KAEhBC,EACKC,IACAzJ,GAAiB,EAAO,YACxBG,IAAgB,EAAO,YACvBrO,GAAKtD,GAAEtB,KACP+E,GAASqF,EAAOpL,IACb6I,GAAYiC,EAAQgF,kBACnBhF,EAAQgF,gBAAgBxM,KAAMqN,EAAW2M,GAI1CxS,EAAQsC,gBACPqE,EAAiB2L,yBAIrBG,IACJzJ,EAAiBZ,EAAU/G,cAAe,YAC1C8H,GAAgBf,EAAU/G,cAAe,YACzCrG,GAAKxD,GAAEtB,KACPiF,GAAYmF,EAAOpL,IAChB6I,GAAYiC,EAAQgF,kBACnBhF,EAAQgF,gBAAgBxM,KAAMqN,EAAW2M,IAiJrD,SAAS7H,GAAgB1C,GACrB,IAAIyK,EAASzK,EAAEyK,OAEZA,GAAU7V,GAAQ6V,EAAQxc,GAAkB,MAkmDnD,SAA8B+R,GAC1BrK,GAAeqK,GAGf,IAAI0K,EAAcpY,GAAMsC,GAAQ9H,KAAMmB,GAAkB,QACxDyb,GAAWna,GAAE7B,IAAagd,KAtmDDna,KAAKka,EAAQzK,GAE9BlO,GAAQ2Y,EA5pBI,eAmoExB,WAEI1U,GAAQ9D,GAAKnF,MAAO,UAx+CGyD,KAAKka,GAEpB3Y,GAAQ2Y,EAAQ1b,IAqkD5B,WAEI,IAAI2W,EAAU9Q,GAAQ9H,KAAMY,IAGxB8C,GAAS1D,KAAMkC,IACZmP,EAAgBC,EAAEG,MACjBqD,GAAc8D,GAGfvH,EAAgBC,EAAEI,OACjBmD,GAAe+D,IA/kDDnV,KAAKka,EAAQzK,GAE3BlO,GAAQ2Y,EAAQ5b,KAAgE,MAAxC+F,GAAQ6V,EAAQ5b,IAkmDpE,SAA4BmR,GACxBrK,GAAeqK,GAGf,IAAI2F,EAASpW,GAAEhB,GAAoBqG,GAAQ9H,KAAMY,KAAc,GAC3Dmc,EAAUta,GAAEnB,GAAWuX,GAAQrT,GAAMsC,GAAQ9H,KAAM,QAEvDsd,GAAgBzE,EAAQkE,IAxmDDtZ,KAAKka,EAAQzK,GAE5BpL,GAAQ6V,EAAQ1S,EAAQQ,KAAO,uBA0mD3C,SAA0ByH,IACnBzQ,GAAEwI,EAAQQ,MAAM,KAAOR,EAAQU,aAAgBV,EAAQS,QAAQ7I,SAC9DgG,GAAeqK,GACf0B,GAAO5U,KAAKyW,aAAa,sBA5mDRhT,KAAKka,EAAQzK,GAItC,SAASsH,GAAqBtR,EAAW2U,GAErCre,GAAS,MAAQ0J,GAAa2U,EAC9Bre,GAASwT,iBAAiB9J,EAAW2M,IAAqB,GAG9D,SAASA,GAAoB3C,GAEzB,IAAIyK,EAAS7U,MAAMgV,WAAa5K,EAAE6K,eAAiB7K,EAAEyK,OAEjDrb,EAAO4Q,EAAE5Q,KACT0b,GAA0B,EAG3BL,GAAUne,IAAame,GAKd,aAATrb,IACCiR,GAAkC,EAClC6G,WAAW,WACP7G,GAAkC,GACnC,OAKK,eAATjR,GAA0BiR,KAIPtI,EAAQqC,qBAAqBhH,MAAM,KAEzCqE,QAAQ,SAASsT,GAC7B,IAAID,EAAwB,CACxB,IAAIE,EAAuBlZ,GAAQ2Y,EAAQM,GAGvCE,EAA6BrW,GAAQ6V,EAAQM,IAE9CC,GAAwBC,KACnB3S,EAAG+L,OAAOE,uBACVrC,IAAe,GAEnB5J,EAAG+L,OAAOE,uBAAwB,EAClCuG,GAA0B,OAMlCA,GAA2BxS,EAAG+L,OAAOE,wBACrCrC,IAAe,GACf5J,EAAG+L,OAAOE,uBAAwB,KAvClCrC,IAAe,GAwDvB,SAASiF,KACL,IAAI+D,EAAkBna,KAClBoa,EAAiBha,KAElB0M,IAAkBqN,GAAmBpN,IAAiBqN,IACrDtN,EAAgBqN,EAChBpN,EAAeqN,EACftJ,IAAQ,IAoGhB,SAASmE,GAAYN,EAASC,EAAQC,GAClC,IAAIwF,EAA0B,IAAZxF,EACdyF,EAAa,IAAMzF,EAEnB0F,EAAgBhf,GAAS2H,cAAc,OAC3CqX,EAAc5a,UAAYpC,GAC1B8F,GAAQuR,EAAQ2F,GAEhB,IA+FuB5F,EACnB6F,EAhGAC,EAAkBlf,GAAS2H,cAAc,OAC7CuX,EAAgB9a,UAAYlC,GAC5B4F,GAAQuR,EAAQ6F,GAEhBna,GAAI9B,GAAEd,GAAsBiX,GAAU,CAACzC,MAASmI,EAAc,MAE/C,EAAZxF,IACI7N,EAAQkD,gBAwFQyK,EAvFGA,EAwFtB6F,EAAS,CAACjX,GAAsB,+CAAkDA,GAAsB,gDAC5GO,GAAMtF,GAAEhB,GAAoBmX,GAAS,GAAI6F,GAER,SAA9BxT,EAAQmD,oBACP7J,GAAI9B,GAAEL,GAAuBwW,GAAU,CAAC+F,eAAgB,uCAAuC1T,EAAQmD,oBACvG7J,GAAI9B,GAAEN,GAAuByW,GAAU,CAAC+F,eAAgB,eAAgB1T,EAAQmD,kBAAoB,8BAGpGnD,EAAQ4B,gBACR9G,GAAKtD,GAAEN,GAAuByW,KA9F3B3N,EAAQe,kBAi5DnB,SAA6B4M,EAASE,GAClCnS,GAASa,GAAsB,eAAiB3F,GAAa,qBAAsB+W,GACnF,IAAIS,EAAM5W,GAAEX,GAAgB8W,GAAS,GAGrC1S,GAASmT,EAAK,MAAQpO,EAAQgB,mBAE9B,IAAI,IAAIhJ,EAAE,EAAGA,EAAG6V,EAAW7V,IACvB0D,GAASa,GAAsB,4CAA6CgS,GAAkBvW,EAAG,SAAU,iCAAkCR,GAAE,KAAM4W,GAAK,IAI9J9U,GAAI8U,EAAK,CAACuF,cAAe,IAAOvF,EAAI/U,WAAW,EAAK,OAEpD4B,GAASzD,GAAE,IAAKA,GAAE,KAAM4W,GAAK,IAAM7Y,IA95D3Bqe,CAAoBjG,EAASE,IAIrCD,EAAOlO,QAAQ,SAASmU,GACpBva,GAAIua,EAAO,CAAC3I,MAASoI,EAAa,MAE/BtT,EAAQoD,kBACP8K,GAAc2F,KAItB,IAAIC,EAAgBtc,GAAElB,GAAkBqX,GAAS,GAI5B,MAAjBmG,IAAwE,IAA9CvZ,GAAM/C,GAAE5B,IAAqBD,KAAqE,IAA9C4E,GAAM/C,GAAE5B,IAAqBD,KAA+C,IAAzB4E,GAAMuZ,IACvIC,GAAsBD,EAAe,YAErC7Y,GAAS2S,EAAO,GAAIrY,IAkI5B,SAASgZ,GAAkBvW,EAAGgc,GAC1B,OAAOhU,EAAQa,mBAAmB7I,IAC3BgI,EAAQS,QAAQzI,IAChBgc,EAAc,KAAOhc,EAAE,GAgClC,SAASuU,KACL,IAiCI0H,EACAC,EAlCAvG,EAAUnW,GAAE5B,IAAoB,GAEpCqF,GAAS0S,EAASlY,IAElB0e,GAASxG,GACTyG,KACAC,GAAU1G,GAEP3N,EAAQsC,gBACPtC,EAAQwC,sBAAsBiC,YAwB9BwP,EAASK,KACTJ,EAAqBnC,GAAmBkC,EAAOtG,SAC3CsG,EAAOtG,SAAYuG,SAAmD,IAAtBA,GAAqC3Z,GAAM2Z,KAAwB3Z,GAAM8K,MAvB7FtH,GAAWiC,EAAQyE,YACnD8P,GAAa,YAAa,CACtBC,cAAe7G,EACfgB,QAAShB,EACTyD,UAAW,KAGXqD,WAAY9G,EAAQnC,aAAa,eACjCkC,aAAcnT,GAAMoT,EAAShY,MAIlCoI,GAAWiC,EAAQ2E,cAClB4P,GAAa,eAiBrB,SAASjK,KACL,IAAIoK,EAuIwBC,EACxBxI,EACAyI,EA+BoBC,EACpBzD,EAvKJ,IAAIpR,EAAQqB,eAAiBrB,EAAQiB,UAAU,CAC3C,IAAI4T,EAAgB1X,KAChB2X,GAqKJ1D,EAA4B3B,GADRoF,EApKqBA,GAqKA,OAAS,KAKtD5E,EAHAR,EAAaoF,EAKNzD,GA3KC2D,EAAsB,EACtBC,EAAaH,EAAiB7b,KAAoB,EAClDic,EAAa3U,EAAMnH,aAAeH,OAAsB6b,EACxDpH,EAAYjW,GAAE7B,IAGlB,GAAGsf,EACCF,EAAsBtH,EAAS7V,OAAS,OAGvC,GAAIid,EAML,IAAK,IAAI7c,EAAI,EAAGA,EAAIyV,EAAS7V,SAAUI,EACrByV,EAASzV,GAGXqY,WAAa2E,IAErBD,EAAsB/c,QAX9B+c,EAAsB,EA4B1B,GA2FwBJ,EAvGEG,EAwG1B3I,EAAM3U,GAAE5B,IAAoB,GAAGya,UAC/BuE,EAASzI,EAAMnT,MAEJ,MAAZ2b,EAGIxI,GAAOhP,KAFHyX,GAAWzX,KAAiBnE,QA3G3BP,GAASjB,GAAE5B,IAAoB,GAAIH,MACnCwF,GAASzD,GAAE5B,IAAoB,GAAIH,IACnC0F,GAAYqC,GAAShG,GAAE5B,IAAoB,IAAKH,OASpDgD,GAJJic,EAAiBjH,EAASsH,GAIGxf,IAAQ,CACjCia,GAAc,EACd,IAMI0F,EACAC,EAPAC,EAAiB5d,GAAE5B,IAAoB,GACvCyf,EAAsB9a,GAAM6a,EAAgBzf,IAAe,EAC3D2f,EAAYC,GAAab,GACzBD,EAAcC,EAAelJ,aAAa,eAC1CkC,EAAenT,GAAMma,EAAgB/e,IAAe,EACpD6f,EAAche,GAAElB,GAAkBoe,GAAgB,GAGlDe,EAAkB,CAClBjB,cAAeY,EACf1H,aAAcA,EAAc,EAC5B+G,WAAYA,EACZ9F,QAAS+F,EACTU,eAAgBC,EAChBjE,UAAWkE,GAGZE,IACCL,EAAkBK,EAAYhK,aAAa,eAC3C0J,EAAa3a,GAAMib,IAGpBtP,IACCjL,GAASyZ,EAAgBnf,IACzB4F,GAAYqC,GAASkX,GAAiBnf,IAEnCwI,GAAYiC,EAAQ0E,UACnB6P,GAAa,UAAWkB,GAEzB1X,GAAYiC,EAAQyE,YACnB8P,GAAa,YAAakB,GAG9BC,GAAUN,GACVjB,GAASO,GACTL,GAAUK,GAEV1G,GAAmByG,EAAY/G,EAAe,GAE3C1N,EAAQS,QAAQ7I,SAEfsN,EAAsBuP,GAE1BkB,GAAST,EAAYC,EAAiBV,IAI1CpK,aAAarD,GACbA,EAAWmI,WAAW,WAClBK,GAAc,GACf,KAGJxP,EAAQsB,eAEP+I,aAAapD,GAEbA,EAAYkI,WAAW,WAEhBnP,EAAQsB,cAGP9J,GAAE5B,IAAoB,GAAGuD,cAAgB2M,GAEzCxE,MAELtB,EAAQuB,qBAQvB,SAASD,KAEF4E,IAGCF,GAAa,EAEb2L,GAAWna,GAAE5B,IAAoB,IACjCoQ,GAAa,GAuDrB,SAAS4P,GAAUve,GACf,GAAK+O,EAAgBC,EAAEhP,GAAvB,CAIA,IAAIwe,EAA0B,SAATxe,EAAmBoS,GAAkBD,GAE1D,GAAGxJ,EAAQsC,eAAe,CACtB,IAAIwT,EAAa9V,EAAQwC,sBAAsBsT,WAAWte,GAAE5B,IAAoB,IAC5EmgB,EAAkB,SAAT1e,EAAmB,SAAW,MAE3C,GAAiB,MAAdye,EAAoB,CAEnB,IAAG9V,EAAQwC,sBAAsBwT,WAAWD,EAAOD,GAG/C,OAAO,EAFPD,SAMJA,SAIJA,KAOR,SAASvE,GAAgBrJ,GAClBjI,EAAQqB,eAAiB4U,GAAchO,IAAM7B,EAAgBC,EAAEC,IAE9D1I,GAAeqK,GAevB,SAASuJ,GAAiBvJ,GACtB,IAAIuM,EAAgB3X,GAAQoL,EAAEyK,OAAQ/c,KAAgB6B,GAAE5B,IAAoB,GAE5E,GAAIqgB,GAAchO,GAAK,CAEhBjI,EAAQqB,eAEPzD,GAAeqK,GAGnB,IAAIiO,EAAcC,GAAclO,GAEhC2H,EAAYsG,EAAYE,EACxBvG,EAAYqG,EAAYG,EAGpB7e,GAAEhB,GAAoBge,GAAe5c,QAAU0e,KAAKC,IAAI5G,EAAcE,GAAcyG,KAAKC,IAAI7G,EAAcE,IAGtGtK,GAAegR,KAAKC,IAAI5G,EAAcE,GAAczW,KAAmB,IAAM4G,EAAQ4C,mBACpEiN,EAAdF,EACGvJ,EAAgBC,EAAEI,OACjBmD,GAAe4K,GAGhBpO,EAAgBC,EAAEG,MACjBqD,GAAc2K,IAOtBxU,EAAQqB,eAAiB6E,GAGzBoQ,KAAKC,IAAI7G,EAAcE,GAActb,GAAO2E,YAAc,IAAM+G,EAAQ4C,mBACtDgN,EAAdF,EACAkG,GAAU,QACSlG,EAAZE,GACPgG,GAAU,QAW9B,SAASK,GAAchO,GAEnB,YAAgC,IAAlBA,EAAEuO,aAAgD,SAAjBvO,EAAEuO,YAMrD,SAASjF,GAAkBtJ,GAOvB,GAJGjI,EAAQsB,eACP4G,GAAkB,GAGnB+N,GAAchO,GAAG,CAChB,IAAIiO,EAAcC,GAAclO,GAChCyH,EAAcwG,EAAYE,EAC1BzG,EAAcuG,EAAYG,GAOlC,SAASI,GAAWC,EAAUC,GAM1B,IALA,IAAIC,EAAM,EAGNC,EAAeH,EAASI,MAAMR,KAAKS,IAAIL,EAAS9e,OAAS+e,EAAQ,IAE7D3e,EAAI,EAAGA,EAAI6e,EAAajf,OAAQI,IACpC4e,GAAYC,EAAa7e,GAG7B,OAAOse,KAAKU,KAAKJ,EAAID,GAWzB,SAAS7F,GAAkB7I,GACvB,IAAIgP,GAAU,IAAIlH,MAAOC,UACrBkH,EAAiBze,GAASjB,GAn7CV,kBAm7C4B,GAAIxB,IAGpD,IAAKoQ,EAAgBC,EAAEE,OAASH,EAAgBC,EAAEC,GAE9C,OADA1I,GAAeqK,IACR,EAIX,GAAGjI,EAAQqB,gBAAkB+D,IAAmB8R,EAAe,CAG3D,IAAIpY,GADJmJ,EAAIA,GAAK3T,GAAOuJ,OACFsZ,aAAelP,EAAEmP,SAAWnP,EAAE7J,OACxCiZ,EAAQf,KAAKS,KAAK,EAAGT,KAAKgB,IAAI,EAAGxY,IAEjCyY,OAA+C,IAAlBtP,EAAEuP,kBAAmD,IAAbvP,EAAEwP,OACvEC,EAAyBpB,KAAKC,IAAItO,EAAEuP,aAAelB,KAAKC,IAAItO,EAAEkP,aAAiBb,KAAKC,IAAItO,EAAEwP,QAAWnB,KAAKC,IAAItO,EAAEmP,UAAYG,EAGzG,IAApBpR,EAAWvO,QACVuO,EAAWwR,QAIfxR,EAAW7G,KAAKgX,KAAKC,IAAIzX,IAGtBkB,EAAQiB,WACPrD,GAAeqK,GAInB,IAAI2P,EAAWX,EAAQnH,EAUvB,GATAA,EAAWmH,EAIG,IAAXW,IAECzR,EAAa,IAGdD,EAAU,CACT,IAAI2R,EAAapB,GAAWtQ,EAAY,IACpBsQ,GAAWtQ,EAAY,KACtB0R,GAGAH,GAGb9B,GADAyB,EAAQ,EACE,OAIA,MAKtB,OAAO,EAGRrX,EAAQsB,eAEP4G,GAAkB,GAQ1B,SAAS+J,GAAUb,EAAWzD,GAC1B,IAAI6G,EAA2B,MAAX7G,EAAkBnW,GAAE5B,IAAoB,GAAK+X,EAC7DC,EAASpW,GAAEhB,GAAoBge,GAAe,GAGlD,KAAc,MAAV5G,GAAkBtI,GAAe9N,GAAEnB,GAAWuX,GAAQhW,OAAS,GAAnE,CAIA,IAAIkgB,EAAetgB,GAAElB,GAAkBsX,GAAQ,GAC3CkE,EAAU,KASd,GAAc,OANVA,EADa,SAAdV,EACWpX,GAAU8d,EAAczhB,IAExB4D,GAAU6d,EAAczhB,KAInB,CAEf,IAAK2J,EAAQ4B,eAAgB,OAE7B,IAAImW,EAAgBva,GAASsa,GAEzBhG,EADa,SAAdV,EACW2G,EAAcA,EAAcngB,OAAS,GAErCmgB,EAAc,GAIhCzS,GAAuB/E,EAAGxH,KAAKif,UAC/B3F,GAAgBzE,EAAQkE,EAASV,IAOrC,SAAS6G,KAEL,IADA,IAAIC,EAAe1gB,GAAElB,IACZ0B,EAAG,EAAGA,EAAEkgB,EAAatgB,OAAQI,IAClC+b,GAAsBmE,EAAalgB,GAAI,YA8C/C,SAAS2Z,GAAWhD,EAAShP,EAAUwY,GACnC,GAAc,MAAXxJ,EAAH,CAEA,IAxC4BA,EACxByJ,EACAC,EAGAlN,EACAmN,EACAC,EACAzV,EAiCAqS,EACAD,EAGAsD,EAAI,CACJ7J,QAASA,EACThP,SAAUA,EACVwY,aAAcA,EACdM,MAhDAL,GADwBzJ,EAwCMA,GAvCNxV,aACxBkf,EAAa1J,EAAQ0B,UAIrBiI,EAAgCrI,GADhC9E,EAAWkN,GAEXE,EAAgBpN,EAAWrF,EAAgBsS,EAC3CtV,EAAyB9C,EAAQ8C,uBAGlBgD,EAAhBsS,GAEKE,GAAoBxV,IAAqD,WAA3BA,IAC9CqI,EAAWoN,IAKXD,GAAoBtS,GAA+B,MAAjB5L,GAAKuU,MAE3CxD,EAAWoN,GASftI,EAAkB9E,GAoBdmK,UAAWC,GAAa5G,GACxB8F,WAAY9F,EAAQnD,aAAa,eACjCkC,aAAcnT,GAAMoU,EAAShZ,IAC7B6f,YAAahe,GAAElB,GAAkBqY,GAAS,GAC1C6F,cAAehd,GAAE5B,IAAoB,GACrCwf,eAAgB7a,GAAM/C,GAAE5B,IAAqBD,IAAe,EAI5D+iB,gBAAiB1S,GAIrB,KAAIwS,EAAEhE,eAAiB7F,IAAY3I,GAAgBhG,EAAQiB,WAAa9D,OAAmBqb,EAAEC,OAAShgB,GAASkW,EAAS5Y,KAAxH,CAQA,GANoB,MAAjByiB,EAAEhD,cACDL,EAAkBqD,EAAEhD,YAAYhK,aAAa,eAC7C0J,EAAa3a,GAAMie,EAAEhD,eAIrBgD,EAAEE,gBAAgB,CAClB,IAAItH,EAAYoH,EAAElD,UAUlB,QAP2B,IAAjB6C,IACN/G,EAAY+G,EAAe,KAAO,QAItCK,EAAEpH,UAAYA,EAEXrT,GAAWiC,EAAQ0E,WACgB,IAA/B6P,GAAa,UAAWiE,GACvB,OAMRxY,EAAQqB,eAAiBrB,EAAQ6B,yBAAkD,IAApB2W,EAAc,gBAC1EA,EAAEL,cAA+B,MAAfK,EAAElD,WACtBkD,EAAEL,cAA+B,QAAfK,EAAElD,cAuMGkD,EArMGA,GAuMxBL,aAMHlb,GAAOzF,GAAE5B,IAAoB,GAAI2J,GAAQiZ,EAAEhE,cAAe7e,KAJ1DmH,GAAMtF,GAAE5B,IAAoB,GAAI4J,GAAQgZ,EAAEhE,cAAe7e,IAAagjB,WAQ1E9N,GAAarT,GAAE5B,IAAoB,GAAGya,WAGtC4H,KAGAO,EAAEI,mBAAqBJ,EAAEhE,cAGzBgE,EAAEC,KAAOD,EAAE7J,QAAQ0B,UACnBmI,EAAElD,UAAYC,GAAaiD,EAAE7J,SA3NzB6J,EA6NGA,GAzNHA,EAAEE,iBACFhD,GAAU8C,EAAEhE,eAGbxU,EAAQsC,gBACPtC,EAAQwC,sBAAsBqW,cAGlC5d,GAAS0T,EAASpZ,IAClB4F,GAAYqC,GAASmR,GAAUpZ,IAC/B4e,GAASxF,GAEN3O,EAAQsC,gBACPtC,EAAQwC,sBAAsBkC,UAKlCwB,EAAqB3F,EAAGxH,KAAKif,UAE7BrC,GAAST,EAAYC,EAAiBqD,EAAE/D,WAAY+D,EAAE9K,cAwG1D,SAAyB8K,GAErB,GAAIxY,EAAQmB,MAAQnB,EAAQqB,gBAAkBrB,EAAQiB,UAAW,CAI7D,IAAImL,EAAc,qBAAuBkK,KAAKwC,MAAMN,EAAEC,MAAQ,WAC9DM,GAAmB3M,GAAa,GAI7BpM,EAAQoB,gBACPiJ,aAAavD,GACbA,EAAsBqI,WAAW,WAC7B6J,GAAkBR,IACnBxY,EAAQoB,iBAEX4X,GAAkBR,OAKtB,CACA,IAAIlI,EAAiBC,GAAkBiI,EAAEC,MACzClY,EAAGxH,KAAKoT,KAAOqM,EAAEC,KAAO,KAExB3M,GAASwE,EAAe3B,QAAS2B,EAAetQ,QAASA,EAAQoB,eAAgB,WAC1EpB,EAAQiB,UAQPkO,WAAW,WACP6J,GAAkBR,IACpB,IAEFQ,GAAkBR,MA7I9BS,CAAgBT,GAGhBtT,EAAsBsT,EAAE/D,WAGxBzG,GAAmBwK,EAAE/D,WAAY+D,EAAE9K,eAqKvC,IAAgC8K,EA9JhC,SAASjE,GAAatW,EAAWua,GAC7B,IAmyESU,EAlwESjb,EAAWua,EACzBW,EAlCAC,GAiCcnb,EAjCWA,EAiCAua,EAjCWA,GAyEpCW,EArCAnZ,EAAQwE,aAqCS,CACbG,YAAa,WAAY,MAAO,CAACkB,IACjCnB,QAAS,WAAY,MAAO,CAAC8T,EAAEhE,cAAegE,EAAEpD,eAAiBoD,EAAE9K,aAAe,EAAI8K,EAAEpH,YACxF3M,UAAW,WAAY,MAAO,CAAC+T,EAAE7J,QAAS6J,EAAE/D,WAAa+D,EAAE9K,aAAe,IAC1E5I,eAAgB,WAAY,MAAO,CAAC0T,EAAE1G,QAAS0G,EAAE/D,WAAa+D,EAAE9K,aAAe,EAAI8K,EAAE3G,YAAa2G,EAAEtD,aACpGnQ,aAAc,WAAY,MAAO,CAACyT,EAAEa,UAAWb,EAAE/D,WAAa+D,EAAE9K,aAAe,EAAI8K,EAAEc,eAAgBd,EAAEpH,UAAWoH,EAAEtD,cAvCvG,CACbvQ,YAAa,WACT,MAAO,CACHgJ,QAAS4L,GAAc/hB,GAAE5B,IAAoB,IAC7Cie,MAAO3H,GAAY1U,GAAElB,GAAkBkB,GAAE5B,IAAoB,IAAI,MAGzE8O,QAAS,WACL,MAAO,CACH8U,OAAQD,GAAcf,EAAEhE,eACxBiF,YAAaF,GAAcf,EAAE7J,SAC7ByC,UAAWoH,EAAEpH,YAIrB3M,UAAW,WACP,OAAO0U,EAAezU,WAG1BI,eAAgB,WACZ,MAAO,CACH6I,QAAS4L,GAAcf,EAAE7K,SACzB6L,OAAQtN,GAAYsM,EAAEa,WACtBI,YAAavN,GAAYsM,EAAE1G,SAC3BV,UAAWoH,EAAEpH,YAIrBrM,aAAc,WACV,OAAOoU,EAAerU,oBAcZ7G,MAhFtB,GAAI+B,EAAQwE,cAQR,IAAkE,IAA/DxE,EAAQ/B,GAAWyb,MAAMN,EAAU,GAAIA,EAAUtC,MAAM,IACtD,OAAO,OANX,GAFA9Y,GAAQ6H,EAAW5H,EAAWmb,IAE4D,IAAvFpZ,EAAQ/B,GAAWyb,MAAMN,EAAU/gB,OAAOqU,KAAK0M,GAAW,KA8xExDF,EA9xEqEE,EA+xE3E/gB,OAAOqU,KAAKwM,GAAYS,IAAI,SAASxhB,GACzC,OAAO+gB,EAAW/gB,OA/xET,OAAO,EASf,OAAO,EAMX,SAASohB,GAAc7gB,GACnB,OAAOA,EAAK,IAAIsT,GAAQtT,GAAM,KAGlC,SAASwT,GAAYxT,GACjB,OAAOA,EAAK,IAAIkhB,GAAMlhB,GAAM,KA8GhC,SAAS6X,GAAkBpE,GACvB,IAAI0N,EAAS,GAcb,OAXG7Z,EAAQqB,gBAAkBrB,EAAQiB,WACjC4Y,EAAO7Z,SAAWmM,EAClB0N,EAAOlL,QAAUnX,GAAEvC,IAAa,KAKhC4kB,EAAO7Z,QAAUmM,EACjB0N,EAAOlL,QAAUra,IAGdulB,EA2DX,SAASb,GAAmBR,GAvB5B,IAA4CA,EAGZ,OAHYA,EAwBNA,GArB5BI,qBAIFJ,EAAEL,aACFlb,GAAOzF,GAAE7B,IAAa,GAAI6iB,EAAEI,oBAG5B9b,GAAMtF,GAAE7B,IAAa6B,GAAE7B,IAAaiC,OAAO,GAAI4gB,EAAEI,oBAGrD/N,GAAarT,GAAE5B,IAAoB,GAAGya,WAGtC4H,MAUGla,GAAWiC,EAAQyE,aAAe+T,EAAEE,iBACnCnE,GAAa,YAAaiE,GAG3BxY,EAAQsC,gBACPtC,EAAQwC,sBAAsBiC,YAG9B+T,EAAEE,iBACFrE,GAAUmE,EAAE7J,SAGhB1T,GAASud,EAAE7J,QAASlZ,IACpB0F,GAAYqC,GAASgb,EAAE7J,SAAUlZ,IACjC2e,KAEAlO,GAAY,EAETnI,GAAWya,EAAE7Y,WACZ6Y,EAAE7Y,WAQV,SAASmL,GAAO6D,EAASmL,GACrBnL,EAAQlD,aAAaqO,EAAWnL,EAAQnD,aAAa,QAAUsO,IAC/DnL,EAAQjD,gBAAgB,QAAUoO,GAOtC,SAAS1F,KACL,IAAI2F,EAAwBviB,GAt6DR,mBAs6D2B,IAAM+a,MAAsB/a,GAp6DlD,8BAo6DgF,GAGpGwI,EAAQiF,aAAgB8U,GAK7BviB,GAAE7B,GAAc,QAAUH,GAAa,KAAKkK,QAAQ,SAASiO,GA5rBjE,IACQqM,EACA7N,EACAyI,EAFAoF,EA4rBuBrM,EA5rBbsM,wBACV9N,EAAM6N,EAAK7N,IACXyI,EAASoF,EAAKpF,QAQAzI,EAFA,EAEoBrG,GAAuB,EAANqG,GAFrC,EAGGyI,GAAwBA,EAAS9O,IAkrB9CqO,GAASxG,KAQrB,SAASwG,GAASrC,GACT9R,EAAQiF,aAMbzN,GAAE,6HAFU0iB,GAAkBpI,IAEyGpS,QAAQ,SAASiP,GAWpJ,GAVA,CAAC,MAAO,UAAUjP,QAAQ,SAASrI,GAC/B,IAAIyiB,EAAYnL,EAAQnD,aAAa,QAAUnU,GAC/B,MAAbyiB,GAAqBA,IACpBhP,GAAO6D,EAAStX,GAChBsX,EAAQ5G,iBAAiB,OAAQ,WAC7BoS,GAAYrI,QAKrB/X,GAAQ4U,EAAS,UAAU,CAC1B,IAAIyL,EAAiBvd,GAAQ8R,EAAS,gBACnCyL,IACCA,EAAcC,OACdD,EAAcE,aAAe,WACzBH,GAAYrI,QAWhC,SAASqI,GAAYxM,GACd3N,EAAQsC,iBACP+H,aAAalC,GACbA,EAAkBgH,WAAW,WACzBxI,EAAiB4T,gBAAgB5M,IAClC,MAOX,SAAS0G,GAAUvC,GACf,IAAI0I,EAAQN,GAAkBpI,GAG9Bta,GAAE,eAAgBgjB,GAAO9a,QAAQ,SAASiP,GAClCA,EAAQ8L,aAAa,kBAA4C,mBAAjB9L,EAAQ+L,MACxD/L,EAAQ+L,SAKhBljB,GAAE,oCAAqCgjB,GAAO9a,QAAQ,SAASiP,GACtDA,EAAQ8L,aAAa,kBACtBE,GAAYhM,GAIhBA,EAAQiM,OAAS,WACRjM,EAAQ8L,aAAa,kBACtBE,GAAYhM,MAS5B,SAASgM,GAAYhM,GACjBA,EAAQkM,cAAcC,YAAY,mDAAoD,KAM1F,SAASpF,GAAU5D,GACf,IAAI0I,EAAQN,GAAkBpI,GAG9Bta,GAAE,eAAgBgjB,GAAO9a,QAAQ,SAASiP,GACjCA,EAAQ8L,aAAa,qBAAgD,mBAAlB9L,EAAQoM,OAC5DpM,EAAQoM,UAKhBvjB,GAAE,oCAAqCgjB,GAAO9a,QAAQ,SAASiP,GACvD,wBAAwB5V,KAAK4V,EAAQnD,aAAa,UAAYmD,EAAQ8L,aAAa,qBACnF9L,EAAQkM,cAAcC,YAAY,oDAAoD,OAQlG,SAASZ,GAAkBpI,GACvB,IAAI+B,EAAQrc,GAAElB,GAAkBwb,GAKhC,OAJI+B,EAAMjc,SACNka,EAAU+B,EAAM,IAGb/B,EAMX,SAAS5C,KACL,IAAIzO,EAAW6T,KACX1C,EAAgBnR,EAAQkN,QACxBkE,EAAcpR,EAAQoT,MAEvBjC,IACI5R,EAAQgD,cACPgP,GAAmBJ,EAAeC,GAElCnI,GAAakI,EAAeC,IASxC,SAAStH,KACL,IAAIiF,IAAgBxP,EAAQU,YAAY,CACpC,IAAID,EAAU6T,KACV1C,EAAgBnR,EAAQkN,QACxBkE,EAAcpR,EAAQoT,MAGtBmH,OAAoD,IAAxB9V,EAC5B+V,OAAoD,IAAxB/V,QAA8D,IAAhB2M,IAAgCvM,EAE3GsM,GAAiBA,EAAcha,SAIzBga,GAAiBA,IAAkB1M,IAAyB8V,GAC1DC,IACE3V,GAAeH,GAAqB0M,IAEzCG,GAAmBJ,EAAeC,IAOlD,SAASyC,KACL,IAAI3G,EACAkG,EACAqH,EAAO5mB,GAAO6mB,SAASD,KAE3B,GAAGA,EAAKtjB,OAAO,CAEX,IAAIwjB,EAAgBF,EAAK1f,QAAQ,IAAK,IAAIH,MAAM,KAG5CggB,GAAsC,EAAtBH,EAAK/a,QAAQ,MAEjCwN,EAAU0N,EAAgB,IAAMD,EAAa,GAAKE,mBAAmBF,EAAa,IAElF,IAAIvJ,EAAcwJ,EAAgBD,EAAa,GAAKA,EAAa,GAC9DvJ,GAAeA,EAAYja,SAC1Bic,EAAQyH,mBAAmBzJ,IAInC,MAAO,CACHlE,QAASA,EACTkG,MAAOA,GAKf,SAASpJ,GAAexC,GACpBoC,aAAanD,GAEb,IAAIqU,EAAgBhnB,GAASgnB,cACzBC,EAAUvT,EAAEuT,QAGD,IAAZA,EAyDP,SAAevT,GACX,IAiDoBA,EAChBwT,EACAC,EACAC,EACAC,EACAC,EACAC,EAvDAC,EAAiB9T,EAAE+T,SACnBT,EAAgBhnB,GAASgnB,cACzBU,EAAoBC,GAAchC,GAAkB1iB,GAAE5B,IAAoB,KAE9E,SAASumB,EAAqBlU,GAE1B,OADArK,GAAeqK,GACRgU,EAAkB,GAAKA,EAAkB,GAAGG,QAAU,MA2C7CnU,EAvCFA,EAwCdwT,EAAgBS,GAAc3nB,IAC9BmnB,EAAoBD,EAActb,QAAQ5L,GAASgnB,eACnDI,EAAwB1T,EAAE+T,SAAWN,EAAoB,EAAIA,EAAoB,EACjFE,EAAmBH,EAAcE,GACjCE,EAAuB3P,GAAYrP,GAAQ+e,EAAkBvlB,KAC7DylB,EAAyBvC,GAAc1c,GAAQ+e,EAAkBjmB,KAE7DkmB,GAAyBC,KA1C9BP,EACsG,MAAlG1e,GAAQ0e,EAAe3lB,GAAqB,IAAMA,GAAqB,IAAMU,MAC5EilB,EAAgBY,EAAqBlU,IAMzCkU,EAAqBlU,KAKrB8T,GAAkBR,GAAiBU,EAAkBA,EAAkBrkB,OAAS,IAChFmkB,GAAkBR,GAAiBU,EAAkB,KAErDre,GAAeqK,IAxFfoU,CAAMpU,GAGDlO,GAAQwhB,EAAe,aAAgBxhB,GAAQwhB,EAAe,UAAaxhB,GAAQwhB,EAAe,WACrD,SAAlDA,EAAc/P,aAAa,oBAAmF,KAAlD+P,EAAc/P,aAAa,qBACvFxL,EAAQ+C,oBAAqB/C,EAAQqB,iBAIF,EADjB,CAAC,GAAI,GAAI,GAAI,GAAI,IACpBlB,QAAQqb,IACnB5d,GAAeqK,GAGnB7C,EAAiB6C,EAAEqU,QAEnBpV,EAAYiI,WAAW,YA4J/B,SAAmBlH,GACf,IAAIsU,EAAetU,EAAE+T,SACjBT,EAAgBhnB,GAASgnB,cACzBiB,EAAiBziB,GAAQwhB,EAAe,UAAYxhB,GAAQwhB,EAAe,SAG/E,GAAIrV,KAAa,CAAC,GAAG,IAAI/F,QAAQ8H,EAAEuT,SAAW,GAI9C,OAAQvT,EAAEuT,SAEN,KAAK,GACL,KAAK,GACEpV,EAAgBM,EAAEJ,IACjBkD,KAEJ,MAGJ,KAAK,GAED,GAAG+S,GAAgBnW,EAAgBM,EAAEJ,KAAOkW,EAAe,CACvDhT,KACA,MAGR,KAAK,GACL,KAAK,GACEpD,EAAgBM,EAAEH,OAEA,KAAd0B,EAAEuT,SAAmBgB,GACpB/S,MAGR,MAGJ,KAAK,GACErD,EAAgBM,EAAEJ,IACjBqD,GAAO,GAEX,MAGJ,KAAK,GACGvD,EAAgBM,EAAEH,MAClBoD,GAAQnS,GAAE7B,IAAaiC,QAE3B,MAGJ,KAAK,GACEwO,EAAgBM,EAAEF,MACjBqD,KAEJ,MAGJ,KAAK,GACEzD,EAAgBM,EAAED,OACjBmD,MAxNJ6S,CAAUxU,IACZ,MAUV,SAASyC,GAAazC,GACfhC,IACCb,EAAiB6C,EAAEqU,SAK3B,SAAStL,GAAiB/I,GAEP,GAAXA,EAAEyU,QACFxM,EAAWjI,EAAE0U,MACb9W,EAAUkC,iBAAiB,YAAa6U,KAKhD,SAAS3L,GAAehJ,GAEL,GAAXA,EAAEyU,OACF7W,EAAUmC,oBAAoB,YAAa4U,IAiDnD,SAASV,GAAcxjB,GACnB,MAAO,GAAGoe,MAAMte,KAAKhB,GAAEkQ,EAAyBhP,IAAKgF,OAAO,SAAS9D,GAC7D,MAAyC,OAAlCA,EAAK4R,aAAa,aAEA,OAAtB5R,EAAKijB,eAoCpB,SAASvN,KAELlK,EADAa,GAAkB,EA8GtB,SAAS2W,GAAiB3U,GAClBjI,EAAQqB,gBAGT6E,IAEK+B,EAAE0U,MAAQzM,GAAY9J,EAAgBC,EAAEC,GACxCkD,KAIIvB,EAAE0U,MAAQzM,GAAY9J,EAAgBC,EAAEE,MAC5CkD,MAGRyG,EAAWjI,EAAE0U,OAMjB,SAAStK,GAAgBzE,EAAQkE,EAASV,GACtC,IAgRkB0L,EAAWC,EAhRzBpP,EAAU9Q,GAAQ+Q,EAAQjY,IAC1B6iB,EAAI,CACJ5K,OAAQA,EACRkE,QAASA,EACTV,UAAWA,EACX4L,WAAY,CAACxW,KAAMsL,EAAQmL,YAC3B/H,WAAY3a,GAAMuX,GAClBnE,QAASA,EACTD,aAAcnT,GAAMoT,EAAShY,IAC7B8e,WAAY9G,EAAQnC,aAAa,eACjC0R,UAAW1lB,GAAEX,GAAgB8W,GAAS,GACtCkE,YAAasL,GAAUrL,GACvBuH,UAAW7hB,GAAElB,GAAkBqX,GAAS,GACxC2L,eAAgB/e,GAAM/C,GAAElB,GAAkBqX,GAAS,IAInD+K,gBAAiB1S,GAErBwS,EAAE4E,WA6PgBN,EA7PStE,EAAEc,eA6PAyD,EA7PgBvE,EAAEtD,WA8P3C4H,GAAaC,EACN,OAEIA,EAAZD,EACQ,OAEJ,SAnQPtE,EAAEpH,UAAYoH,EAAEpH,UAAYoH,EAAEpH,UAAYoH,EAAE4E,UAGxC5E,EAAEE,kBAEFxS,GAAY,GAGblG,EAAQ+E,eAGHyT,EAAEE,iBAAiC,SAAdF,EAAE4E,WACpBrf,GAAYiC,EAAQ+E,gBACqB,IAApCwP,GAAa,eAAgBiE,GAC7BlT,GAAc,GAO9BrK,GAAS6W,EAASvc,IAClB4F,GAAYqC,GAASsU,GAAUvc,IAE3BijB,EAAEE,kBACFhD,GAAU8C,EAAEa,WACZlF,GAASrC,KAGT9R,EAAQ4B,gBAAkB5B,EAAQkD,gBAElCrE,GAAOrH,GAAEN,GAAuByW,GAAyB,IAAf6K,EAAEtD,YAG5CrW,GAAOrH,GAAEL,GAAuBwW,GAA2B,MAAjBvT,GAAK0X,KAIhDrZ,GAASkV,EAASpY,MAAYijB,EAAEE,iBAC/B/C,GAAS6C,EAAEtD,WAAYsD,EAAE3G,YAAa2G,EAAE/D,WAAY+D,EAAE9K,cAgC9D,SAA+BE,EAAQ4K,EAAGjE,GACtC,IAAIyI,EAAaxE,EAAEwE,WAEnB,GAAGhd,EAAQmB,KAAK,CACZ,IAAIiL,EAAc,gBAAkBkK,KAAKwC,MAAMkE,EAAWxW,MAAQ,gBAElEjG,EAAGxH,KAAKsT,aAAamM,EAAE9K,cAAgBtB,EACvC9S,GAAI+jB,GAAa7lB,GAAEd,GAAsBkX,IAAU0P,GAAclR,IAEjErF,EAAoBoI,WAAW,WACxBoF,GACCgJ,GAAgB/E,IAErBxY,EAAQoB,qBAEXb,EAAGxH,KAAKyN,KAAKgS,EAAE9K,cAAgB4I,KAAKwC,MAAMkE,EAAWxW,MAErDsF,GAAS8B,EAAQ0I,KAAKwC,MAAMkE,EAAWxW,MAAOxG,EAAQoB,eAAgB,WAC/DmT,GACCgJ,GAAgB/E,KAhD5BgF,CAAsB5P,EAAQ4K,GAAG,IAIrC,SAAS+E,GAAgB/E,GAqDzB,IAAgC0E,EAAWhI,EAAXgI,EApDL1E,EAAE0E,UAoDchI,EApDHsD,EAAEtD,WAqDnClV,EAAQe,kBAAiC,MAAbmc,IAC3B/hB,GAAY3D,GAAEhC,GAAY0nB,GAAY3nB,IACtC0F,GAAUzD,GAAE,IAAKA,GAAE,KAAM0lB,GAAWhI,IAAe3f,KApDnDijB,EAAEE,kBACC3a,GAAYiC,EAAQ8E,iBACnByP,GAAa,iBAAkBiE,GAKnCtS,GAAY,EAEZmO,GAAUmE,EAAE1G,UAIhBxM,GAAc,EAgDlB,SAASkF,KACLH,aAAazD,GAIbA,EAAWuI,WAAW,WAKlB,IAAI,IAAInX,EAAI,EAAGA,EAAG,EAAGA,IACjB6O,EAAkBsI,WAAWsO,GAAe,IAAMzlB,IAEvD,KAMP,SAASylB,KAML,GAHAha,KAGI8B,EAAe,CACf,IAAIgW,EAAgBhnB,GAASgnB,cAG7B,IAAKxhB,GAAQwhB,EAAe,cAAgBxhB,GAAQwhB,EAAe,WAAaxhB,GAAQwhB,EAAe,UAAW,CAC9G,IAAImC,EAAgB1kB,KAGhBsd,KAAKC,IAAImH,EAAgBvN,GAAmB,GAAKmG,KAAKS,IAAI5G,EAAgBuN,GAAiB,MAC3F5T,IAAQ,GACRqG,EAAiBuN,SAKzBtO,KAQR,SAAS3L,KACL,IAAIka,EAAa3d,EAAQyD,YAAczD,EAAQ0D,gBAC3Cka,EAAc5d,EAAQ2D,iBAGtBka,EAAuBF,GAAcrpB,GAAO+E,WAAaskB,EACzDG,EAAwBF,GAAetpB,GAAO2E,YAAc2kB,EAE7DD,GAAcC,EACb7T,GAAc8T,GAAwBC,GAElCH,EACJ5T,GAAc8T,GAEVD,GACJ7T,GAAc+T,GAOtB,SAAST,GAAa1O,GAClB,IAAI9C,EAAa,OAAS7L,EAAQoB,eAAiB,MAAQpB,EAAQyB,WAGnE,OADAtG,GAAYwT,EAASxZ,IACdmE,GAAIqV,EAAS,CAChB/C,qBAAsBC,EACtBA,WAAcA,IAOtB,SAASF,GAAgBgD,GACrB,OAAO1T,GAAS0T,EAASxZ,IAgC7B,SAAS6Y,GAAmBiG,EAAQ1Z,GAZpC,IAA6BwjB,EAdJA,EAAMrQ,EAcFqQ,EAaL9J,EAZpBzc,GAAEwI,EAAQQ,MAAMd,QAAQ,SAASc,GAC1BR,EAAQQ,MAAgB,MAARA,IACfrF,GAAY3D,GAAEhC,GAAYgL,GAAOjL,IACjC0F,GAASzD,GAAE,qBAAqBumB,EAAK,KAAMvd,GAAOjL,OAlBrCwoB,EA4BL9J,EA5BWvG,EA4BHnT,EA3BrByF,EAAQW,YAAuC,MAAzBnJ,GAAEtB,IAAiB,KACpCiF,GAAY3D,GAAEhC,GAAYgC,GAAEtB,IAAiB,IAAKX,IAElD0F,GADD8iB,EACWvmB,GAAE,YAAcumB,EAAO,KAAMvmB,GAAEtB,IAAiB,IAEjDsB,GAAE,IAAKA,GAAE,KAAMA,GAAEtB,IAAiB,IAAIwX,IAFgBnY,KA+B3E,SAASggB,GAAazD,GAClB,IAAIgL,EAAYviB,GAAM/C,GAAE5B,IAAoB,GAAID,IAC5ConB,EAAUxiB,GAAMuX,EAASnc,IAC7B,OAAImnB,GAAaC,EACN,OAEIA,EAAZD,EACQ,KAEJ,OAiBX,SAAS5O,GAAcS,GAEnB,IAAIlW,GAASkW,EAAShY,IAAO,CACzB,IAAIoF,EAAUxH,GAAS2H,cAAc,OACrCH,EAAQpD,UAAY9C,GACpBkG,EAAQrC,MAAMqR,OAASqH,GAAezD,GAAW,KAEjD1T,GAAS0T,EAAShY,IAClB2F,GAAUqS,EAAS5S,IAI3B,SAASqW,GAAezD,GACpB,IAAIqP,EAAgBlY,EAEpB,GAAG9F,EAAQsD,YAActD,EAAQuD,cAAc,CAC3C,IAAIoK,EAAUgB,EACVlW,GAASkV,EAASjY,MAClBiY,EAAU9Q,GAAQ8R,EAAShZ,KAG/B,IAAIsoB,EAAWC,SAAS/Q,iBAAiBQ,GAAS,gBAAkBuQ,SAAS/Q,iBAAiBQ,GAAS,mBACvGqQ,EAAiBlY,EAAgBmY,EAGrC,OAAOD,EAMX,SAASjF,GAAmB3M,EAAa+R,GAClCA,EACCd,GAAaxX,GAEb8F,GAAgB9F,GAGpBvM,GAAIuM,EAAWyX,GAAclR,IAC7B7L,EAAGxH,KAAKqT,YAAcA,EAGtB+C,WAAW,WACPhU,GAAY0K,EAAW1Q,KACzB,IAMN,SAAS4c,GAAmBH,GACxB,IAAIjE,EAAUnW,GAAE7B,GAAc,iBAAiBic,EAAc,KAAM/L,GAAW,GAC9E,IAAI8H,EAAQ,CACR,IAAID,OAAwC,IAAlBkE,EAAgCA,EAAe,EAAI,EAC7EjE,EAAUnW,GAAE7B,IAAa+X,GAG7B,OAAOC,EAmBX,SAASqE,GAAmBJ,EAAeC,GACvC,IAAIlE,EAAUoE,GAAmBH,GAGjC,GAAc,MAAXjE,EAAH,CAEA,IAnBsBkE,EAAalE,EAC/BkG,EAkBAA,GAjBQ,OADRA,EAAQrc,GAAEnB,GAAY,kBADJwb,EAmBOA,GAlB0B,KADpBlE,EAmBOA,GAlB4B,MAElEkE,OAAqC,IAAhBA,EAA8BA,EAAc,EACjEgC,EAAQrc,GAAEnB,GAAWsX,GAASkE,IAG3BgC,GAeHsJ,GAAUxP,KAAazI,GAAwBzM,GAASkV,EAASpY,IAOjE6oB,GAAavK,GANblC,GAAWhE,EAAS,WAChByQ,GAAavK,MAYzB,SAASuK,GAAavK,GACN,MAATA,GACCxB,GAAgBxV,GAAQgX,EAAOrd,IAAqBqd,GA6B5D,SAAS8B,GAAST,EAAYrD,EAAa4C,EAAY/G,GACnD,IAAI2Q,EAAc,GAEfre,EAAQS,QAAQ7I,SAAWoI,EAAQU,cAG/BwU,GACkB,MAAdT,IACC4J,EAAc5J,GAIA,MAAf5C,IACCA,EAAcqD,GAIlBoJ,GAAWD,EAAc,KADzBlZ,EAAoB0M,MAID,MAAdqD,IACL/P,EAAoB0M,GACpByM,GAAW7J,KASnBzF,KAMJ,SAASsP,GAAWC,GAChB,GAAGve,EAAQiD,cACPkY,SAASD,KAAOqD,OAGhB,GAAGhZ,GAAiBI,EAChBrR,GAAOkqB,QAAQC,kBAAahqB,OAAWA,EAAW,IAAM8pB,OACvD,CACD,IAAIG,EAAUpqB,GAAO6mB,SAASwD,KAAKtjB,MAAM,KAAK,GAC9C/G,GAAO6mB,SAAS3f,QAASkjB,EAAU,IAAMH,IAQrD,SAASpB,GAAUxO,GACf,IAAIA,EACA,OAAO,KAEX,IAAIsF,EAAStF,EAAQnD,aAAa,eAC9BoT,EAAerkB,GAAMoU,GAOzB,OAJa,MAAVsF,IACCA,EAAS2K,GAGN3K,EAMX,SAASjF,KACL,IAAIrB,EAAUnW,GAAE5B,IAAoB,GAChCie,EAAQrc,GAAElB,GAAkBqX,GAAS,GAErCiE,EAAgBuL,GAAUxP,GAC1BkE,EAAcsL,GAAUtJ,GAExBvc,EAAOunB,OAAOjN,GAEfiC,IACCvc,EAAOA,EAAO,IAAMua,GAIxBva,EAAOA,EAAKkE,QAAQ,IAAK,KAAKA,QAAQ,IAAI,IAG1C,IAAIsjB,EAAU,IAAIhmB,OAAO,UAAYxD,GAAiB,cAAe,KACrEgL,EAAM3H,UAAY2H,EAAM3H,UAAU6C,QAAQsjB,EAAS,IAGnD7jB,GAASqF,EAAOhL,GAAiB,IAAMgC,GA+J3C,SAAS6e,GAAclO,GACnB,IAAIV,EAAS,GAWb,OATAA,EAAO6O,OAAwB,IAAZnO,EAAE0U,QAA0B1U,EAAE0U,OAAS1U,EAAE8W,OAAS9W,EAAE0U,MAAQ1U,EAAE+W,QAAQ,GAAGrC,MAC5FpV,EAAO8O,OAAwB,IAAZpO,EAAE8W,QAA0B9W,EAAE0U,OAAS1U,EAAE8W,OAAS9W,EAAE8W,MAAQ9W,EAAE+W,QAAQ,GAAGD,MAGzFpZ,GAAWsQ,GAAchO,IAAMjI,EAAQiB,gBAAkC,IAAdgH,EAAE+W,UAC5DzX,EAAO6O,EAAInO,EAAE+W,QAAQ,GAAGrC,MACxBpV,EAAO8O,EAAIpO,EAAE+W,QAAQ,GAAGD,OAGrBxX,EAOX,SAASwM,GAAsByB,EAAayJ,GACxC/V,GAAkB,EAAG,iBAEK,IAAhB+V,IAENjZ,GAAa,GAGjBqM,GAAgBxV,GAAQ2Y,EAAahf,IAAqBgf,QAEhC,IAAhByJ,IACNjZ,GAAa,GAGjBkD,GAAkBd,EAAUhH,eAAgB,YAMhD,SAASyJ,GAAasB,GAGlB,IAAI+S,EAAa5I,KAAKwC,MAAM3M,GAE5B,GAAInM,EAAQmB,MAAQnB,EAAQqB,gBAAkBrB,EAAQiB,UAElD8X,GADkB,qBAAuBmG,EAAa,YACtB,QAE/B,GAAGlf,EAAQqB,gBAAkBrB,EAAQiB,UACtC3H,GAAIuM,EAAW,CAACsG,KAAQ+S,EAAa,OACrC3e,EAAGxH,KAAKoT,KAAO+S,EAAa,SAE5B,CACA,IAAI5O,EAAiBC,GAAkB2O,GACvCC,GAAa7O,EAAe3B,QAAS2B,EAAetQ,UAO5D,SAASsd,GAAclR,GACnB,MAAO,CACHgT,oBAAqBhT,EACrBiT,iBAAkBjT,EAClBkT,gBAAgBlT,EAChBa,UAAab,GAQrB,SAASiF,GAAmBvS,EAAOsS,EAAW/Z,GAEzB,QAAd+Z,EACChL,EAAgB/O,GAAM+Z,GAAatS,EAKnCzG,OAAOqU,KAAKtG,EAAgB/O,IAAOqI,QAAQ,SAASvH,GAChDiO,EAAgB/O,GAAMc,GAAO2G,IAwJzC,SAASsR,GAAiBmP,EAAUzgB,EAAOzH,GACvC2I,EAAQuf,GAAYzgB,EACR,aAATzH,IACC+Q,EAAUmX,GAAYzgB,GAO9B,SAAS0J,KACL,IAAIgX,EAAIxf,EAAwC,WAC5Cyf,EAAW,qCAEXxf,EAIIuf,GAAKA,EAAE5nB,OAAS,KACpBL,QAAQmoB,KAAK,yFAA0FD,GACvGloB,QAAQmoB,KAAK,uCAAwCD,KALrDroB,GAAU,QAAS,qHACnBA,GAAU,QAAS,wDAOpBqB,GAAS4H,EAAOhL,IACf+B,GAAU,QAAS,kFAKnB4I,EAAQ6B,qBACP7B,EAAQ2B,SAAW3B,EAAQ0B,cAC5B1B,EAAQ6B,oBAAqB,EAC7BzK,GAAU,OAAQ,gHAGnB4I,EAAQsC,iBACPtC,EAAQiB,WAAcjB,EAAQqB,eAC9BjK,GAAU,OAAQ,gKAGnB4I,EAAQ6B,qBAAuB7B,EAAQiB,WAAcjB,EAAQqB,gBAC5DrB,EAAQ6B,oBAAqB,EAC7BzK,GAAU,OAAQ,4IAGnB4I,EAAQsC,gBAAmD,MAAjCtC,EAAQwC,wBACjCxC,EAAQsC,gBAAiB,EACzBlL,GAAU,QAAS,sHAIvBmR,EAAW7I,QAAQ,SAASigB,GAErB3f,EAAQ2f,IACPvoB,GAAU,OAAQ,+GAAgHuoB,KAK1I3f,EAAQS,QAAQf,QAAQ,SAASqe,GAG7B,IAAI6B,EAAW,GAAG9I,MAAMte,KAAKhB,GAAE,WAAWkG,OAAO,SAAS9D,GACtD,OAAOA,EAAK4R,aAAa,SAAW5R,EAAK4R,aAAa,QAAQqU,eAAiB9B,EAAK8B,gBAGpFC,EAAS,GAAGhJ,MAAMte,KAAKhB,GAAE,SAASkG,OAAO,SAAS9D,GAClD,OAAOA,EAAK4R,aAAa,OAAS5R,EAAK4R,aAAa,MAAMqU,eAAiB9B,EAAK8B,gBAGpF,GAAGC,EAAOloB,QAAUgoB,EAAShoB,OAAQ,CACjCR,GAAU,QAAS,4GACnB,IAAI2oB,EAAeD,EAAOloB,OAAS,KAAO,QAEvCkoB,EAAOloB,QAAUgoB,EAAShoB,SACzBR,GAAU,QAAS,IAAM2mB,EAAO,0CAA2CgC,EAAc,kBA+BzG,SAASjU,GAAS6C,EAASqR,EAAIC,EAAUtgB,GACrC,IAvByBgP,EAuBrBuR,GAvBqBvR,EAuBOA,GAnBrBwR,MAAQ7rB,IAAUmE,GAASkW,EAASpY,IAChCoY,EAAQyR,YAEdpgB,EAAQqB,eAAkBrB,EAAQiB,UAC5B9D,KAGAwR,EAAQ0B,UAanBgQ,EAASL,EAAKE,EACdI,EAAc,EAElBpY,GAAkB,EAElB,IAAIqY,EAAgB,WAChB,GAAGrY,EAAgB,CACf,IAAIsY,EAAMR,EAEVM,GAPQ,GASLL,IACCO,EAAMlsB,GAAOmU,WAAWzI,EAAQwB,QAAQ8e,EAAaJ,EAAOG,EAAQJ,IAGxEd,GAAaxQ,EAAS6R,GAEnBF,EAAcL,EACb9Q,WAAWoR,EAhBP,SAiBqB,IAAb5gB,GACZA,SAEE2gB,EAAcL,GACpBtgB,KAIR4gB,IAOJ,SAASpB,GAAaxQ,EAAS6R,IACvBxgB,EAAQqB,eAAiBrB,EAAQiB,WAAc0N,EAAQwR,MAAQ7rB,IAAUmE,GAASkW,EAASpY,IAGxFoY,EAAQwR,MAAQ7rB,IAAWmE,GAASkW,EAASpY,IAC5CoY,EAAQyR,WAAaI,EAIrB7R,EAAQ7C,SAAS,EAAG0U,GAGvB7R,EAAQjV,MAAMyS,IAAMqU,EAAM,KAsBnC,SAASC,GAAK/nB,EAAIjB,GACd1C,KAAKkf,OAASvb,EAAG8S,aAAa,eAC9BzW,KAAK6E,KAAOlB,EACZ3D,KAAKwF,MAAQA,GAAM7B,EAAIjB,GACvB1C,KAAK2rB,OAAS3rB,KAAKwF,QAAU7B,EAAGyG,cAActH,iBAAiBJ,GAAUG,OAAQ,EACjF7C,KAAK4rB,SAAW5rB,KAAKwF,MAMzB,SAASyR,GAAQtT,GACb+nB,GAAKjoB,KAAKzD,KAAM2D,EAAI/C,IAMxB,SAASikB,GAAMlhB,GACX+nB,GAAKjoB,KAAKzD,KAAM2D,EAAIrC,IA15GMmS,QAo+HnClU,OAAOssB,QAAUtsB,OAAOM,UACvB,SAAW4C,EAAG5C,GACV,aAGK4C,GAAM5C,EAKX4C,EAAEqC,GAAGjF,SAAW,SAASoL,GACrBA,EAAUxI,EAAEqpB,OAAO,GAAI7gB,EAAS,CAACxI,EAAKA,IACtC,IAAI5C,EAASG,KAAK,GAAIiL,IANtB1L,OAAOuL,SAASzI,UAAU,QAAS,0DAL3C,CAaG9C,OAAOssB,OAAQtsB,OAAOM", "file": "fullpage.min.js", "sourcesContent": ["/*!\r\n * fullPage 3.0.7\r\n * https://github.com/alvarotrigo/fullPage.js\r\n *\r\n * @license GPLv3 for open source use only\r\n * or Fullpage Commercial License for commercial use\r\n * http://alvarotrigo.com/fullPage/pricing/\r\n *\r\n * Copyright (C) 2018 http://alvarotrigo.com/fullPage - A project by <PERSON><PERSON>\r\n */\r\n(function( root, window, document, factory, undefined) {\r\n    if( typeof define === 'function' && define.amd ) {\r\n        // AMD. Register as an anonymous module.\r\n        define( function() {\r\n            root.fullpage = factory(window, document);\r\n            return root.fullpage;\r\n        } );\r\n    } else if( typeof exports === 'object' ) {\r\n        // Node. Does not work with strict CommonJS.\r\n        module.exports = factory(window, document);\r\n    } else {\r\n        // Browser globals.\r\n        window.fullpage = factory(window, document);\r\n    }\r\n}(this, window, document, function(window, document){\r\n    'use strict';\r\n\r\n    // keeping central set of classnames and selectors\r\n    var WRAPPER =               'fullpage-wrapper';\r\n    var WRAPPER_SEL =           '.' + WRAPPER;\r\n\r\n    // slimscroll\r\n    var SCROLLABLE =            'fp-scrollable';\r\n    var SCROLLABLE_SEL =        '.' + SCROLLABLE;\r\n\r\n    // util\r\n    var RESPONSIVE =            'fp-responsive';\r\n    var NO_TRANSITION =         'fp-notransition';\r\n    var DESTROYED =             'fp-destroyed';\r\n    var ENABLED =               'fp-enabled';\r\n    var VIEWING_PREFIX =        'fp-viewing';\r\n    var ACTIVE =                'active';\r\n    var ACTIVE_SEL =            '.' + ACTIVE;\r\n    var COMPLETELY =            'fp-completely';\r\n    var COMPLETELY_SEL =        '.' + COMPLETELY;\r\n\r\n    // section\r\n    var SECTION_DEFAULT_SEL =   '.section';\r\n    var SECTION =               'fp-section';\r\n    var SECTION_SEL =           '.' + SECTION;\r\n    var SECTION_ACTIVE_SEL =    SECTION_SEL + ACTIVE_SEL;\r\n    var TABLE_CELL =            'fp-tableCell';\r\n    var TABLE_CELL_SEL =        '.' + TABLE_CELL;\r\n    var AUTO_HEIGHT =           'fp-auto-height';\r\n    var AUTO_HEIGHT_SEL =       '.' + AUTO_HEIGHT;\r\n    var AUTO_HEIGHT_RESPONSIVE = 'fp-auto-height-responsive';\r\n    var AUTO_HEIGHT_RESPONSIVE_SEL = '.' + AUTO_HEIGHT_RESPONSIVE;\r\n    var NORMAL_SCROLL =         'fp-normal-scroll';\r\n    var NORMAL_SCROLL_SEL =     '.' + NORMAL_SCROLL;\r\n\r\n    // section nav\r\n    var SECTION_NAV =           'fp-nav';\r\n    var SECTION_NAV_SEL =       '#' + SECTION_NAV;\r\n    var SECTION_NAV_TOOLTIP =   'fp-tooltip';\r\n    var SECTION_NAV_TOOLTIP_SEL='.'+SECTION_NAV_TOOLTIP;\r\n    var SHOW_ACTIVE_TOOLTIP =   'fp-show-active';\r\n\r\n    // slide\r\n    var SLIDE_DEFAULT_SEL =     '.slide';\r\n    var SLIDE =                 'fp-slide';\r\n    var SLIDE_SEL =             '.' + SLIDE;\r\n    var SLIDE_ACTIVE_SEL =      SLIDE_SEL + ACTIVE_SEL;\r\n    var SLIDES_WRAPPER =        'fp-slides';\r\n    var SLIDES_WRAPPER_SEL =    '.' + SLIDES_WRAPPER;\r\n    var SLIDES_CONTAINER =      'fp-slidesContainer';\r\n    var SLIDES_CONTAINER_SEL =  '.' + SLIDES_CONTAINER;\r\n    var TABLE =                 'fp-table';\r\n\r\n    // slide nav\r\n    var SLIDES_NAV =            'fp-slidesNav';\r\n    var SLIDES_NAV_SEL =        '.' + SLIDES_NAV;\r\n    var SLIDES_NAV_LINK_SEL =   SLIDES_NAV_SEL + ' a';\r\n    var SLIDES_ARROW =          'fp-controlArrow';\r\n    var SLIDES_ARROW_SEL =      '.' + SLIDES_ARROW;\r\n    var SLIDES_PREV =           'fp-prev';\r\n    var SLIDES_PREV_SEL =       '.' + SLIDES_PREV;\r\n    var SLIDES_ARROW_PREV =     SLIDES_ARROW + ' ' + SLIDES_PREV;\r\n    var SLIDES_ARROW_PREV_SEL = SLIDES_ARROW_SEL + SLIDES_PREV_SEL;\r\n    var SLIDES_NEXT =           'fp-next';\r\n    var SLIDES_NEXT_SEL =       '.' + SLIDES_NEXT;\r\n    var SLIDES_ARROW_NEXT =     SLIDES_ARROW + ' ' + SLIDES_NEXT;\r\n    var SLIDES_ARROW_NEXT_SEL = SLIDES_ARROW_SEL + SLIDES_NEXT_SEL;\r\n\r\n    function initialise(containerSelector, options) {\r\n        var isOK = options && new RegExp('([\\\\d\\\\w]{8}-){3}[\\\\d\\\\w]{8}|^(?=.*?[A-Y])(?=.*?[a-y])(?=.*?[0-8])(?=.*?[#?!@$%^&*-]).{8,}$').test(options['li'+'cen'+'seK' + 'e' + 'y']) || document.domain.indexOf('al'+'varotri' +'go' + '.' + 'com') > -1;\r\n\r\n        // cache common elements\r\n        var $htmlBody = $('html, body');\r\n        var $html = $('html')[0];\r\n        var $body = $('body')[0];\r\n\r\n        //only once my friend!\r\n        if(hasClass($html, ENABLED)){ displayWarnings(); return; }\r\n\r\n        var FP = {};\r\n\r\n        // Creating some defaults, extending them with any options that were provided\r\n        options = deepExtend({\r\n            //navigation\r\n            menu: false,\r\n            anchors:[],\r\n            lockAnchors: false,\r\n            navigation: false,\r\n            navigationPosition: 'right',\r\n            navigationTooltips: [],\r\n            showActiveTooltip: false,\r\n            slidesNavigation: false,\r\n            slidesNavPosition: 'bottom',\r\n            scrollBar: false,\r\n            hybrid: false,\r\n\r\n            //scrolling\r\n            css3: true,\r\n            scrollingSpeed: 700,\r\n            autoScrolling: true,\r\n            fitToSection: true,\r\n            fitToSectionDelay: 1000,\r\n            easing: 'easeInOutCubic',\r\n            easingcss3: 'ease',\r\n            loopBottom: false,\r\n            loopTop: false,\r\n            loopHorizontal: true,\r\n            continuousVertical: false,\r\n            continuousHorizontal: false,\r\n            scrollHorizontally: false,\r\n            interlockedSlides: false,\r\n            dragAndMove: false,\r\n            offsetSections: false,\r\n            resetSliders: false,\r\n            fadingEffect: false,\r\n            normalScrollElements: null,\r\n            scrollOverflow: false,\r\n            scrollOverflowReset: false,\r\n            scrollOverflowHandler: window.fp_scrolloverflow ? window.fp_scrolloverflow.iscrollHandler : null,\r\n            scrollOverflowOptions: null,\r\n            touchSensitivity: 5,\r\n            touchWrapper: typeof containerSelector === 'string' ? $(containerSelector)[0] : containerSelector,\r\n            bigSectionsDestination: null,\r\n\r\n            //Accessibility\r\n            keyboardScrolling: true,\r\n            animateAnchor: true,\r\n            recordHistory: true,\r\n\r\n            //design\r\n            controlArrows: true,\r\n            controlArrowColor: '#fff',\r\n            verticalCentered: true,\r\n            sectionsColor : [],\r\n            paddingTop: 0,\r\n            paddingBottom: 0,\r\n            fixedElements: null,\r\n            responsive: 0, //backwards compabitility with responsiveWiddth\r\n            responsiveWidth: 0,\r\n            responsiveHeight: 0,\r\n            responsiveSlides: false,\r\n            parallax: false,\r\n            parallaxOptions: {\r\n                type: 'reveal',\r\n                percentage: 62,\r\n                property: 'translate'\r\n            },\r\n            cards: false,\r\n            cardsOptions: {\r\n                perspective: 100,\r\n                fadeContent: true,\r\n                fadeBackground: true\r\n            },\r\n\r\n            //Custom selectors\r\n            sectionSelector: SECTION_DEFAULT_SEL,\r\n            slideSelector: SLIDE_DEFAULT_SEL,\r\n\r\n            //events\r\n            v2compatible: false,\r\n            afterLoad: null,\r\n            onLeave: null,\r\n            afterRender: null,\r\n            afterResize: null,\r\n            afterReBuild: null,\r\n            afterSlideLoad: null,\r\n            onSlideLeave: null,\r\n            afterResponsive: null,\r\n\r\n            lazyLoading: true\r\n        }, options);\r\n\r\n        //flag to avoid very fast sliding for landscape sliders\r\n        var slideMoving = false;\r\n\r\n        var isTouchDevice = navigator.userAgent.match(/(iPhone|iPod|iPad|Android|playbook|silk|BlackBerry|BB10|Windows Phone|Tizen|Bada|webOS|IEMobile|Opera Mini)/);\r\n        var isTouch = (('ontouchstart' in window) || (navigator.msMaxTouchPoints > 0) || (navigator.maxTouchPoints));\r\n        var container = typeof containerSelector === 'string' ? $(containerSelector)[0] : containerSelector;\r\n        var windowsHeight = getWindowHeight();\r\n        var windowsWidth = getWindowWidth();\r\n        var isResizing = false;\r\n        var isWindowFocused = true;\r\n        var lastScrolledDestiny;\r\n        var lastScrolledSlide;\r\n        var canScroll = true;\r\n        var scrollings = [];\r\n        var controlPressed;\r\n        var startingSection;\r\n        var isScrollAllowed = {};\r\n        isScrollAllowed.m = {  'up':true, 'down':true, 'left':true, 'right':true };\r\n        isScrollAllowed.k = deepExtend({}, isScrollAllowed.m);\r\n        var MSPointer = getMSPointer();\r\n        var events = {\r\n            touchmove: 'ontouchmove' in window ? 'touchmove' :  MSPointer.move,\r\n            touchstart: 'ontouchstart' in window ? 'touchstart' :  MSPointer.down\r\n        };\r\n        var scrollBarHandler;\r\n\r\n        // taken from https://github.com/udacity/ud891/blob/gh-pages/lesson2-focus/07-modals-and-keyboard-traps/solution/modal.js\r\n        var focusableElementsString = 'a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), iframe, object, embed, [tabindex=\"0\"], [contenteditable]';\r\n\r\n        //cheks for passive event support\r\n        var g_supportsPassive = false;\r\n        try {\r\n          var opts = Object.defineProperty({}, 'passive', {\r\n            get: function() {\r\n              g_supportsPassive = true;\r\n            }\r\n          });\r\n          window.addEventListener(\"testPassive\", null, opts);\r\n          window.removeEventListener(\"testPassive\", null, opts);\r\n        } catch (e) {}\r\n\r\n        //timeouts\r\n        var resizeId;\r\n        var resizeHandlerId;\r\n        var afterSectionLoadsId;\r\n        var afterSlideLoadsId;\r\n        var scrollId;\r\n        var scrollId2;\r\n        var keydownId;\r\n        var g_doubleCheckHeightId;\r\n        var originals = deepExtend({}, options); //deep copy\r\n        var activeAnimation;\r\n        var g_initialAnchorsInDom = false;\r\n        var g_canFireMouseEnterNormalScroll = true;\r\n        var g_mediaLoadedId;\r\n        var extensions = [\r\n            'parallax',\r\n            'scrollOverflowReset',\r\n            'dragAndMove',\r\n            'offsetSections',\r\n            'fadingEffect',\r\n            'responsiveSlides',\r\n            'continuousHorizontal',\r\n            'interlockedSlides',\r\n            'scrollHorizontally',\r\n            'resetSliders',\r\n            'cards'\r\n        ];\r\n\r\n        displayWarnings();\r\n\r\n        //easeInOutCubic animation included in the plugin\r\n        window.fp_easings = deepExtend(window.fp_easings, {\r\n            easeInOutCubic: function (t, b, c, d) {\r\n                if ((t/=d/2) < 1) return c/2*t*t*t + b;return c/2*((t-=2)*t*t + 2) + b;\r\n            }\r\n        });\r\n\r\n        /**\r\n        * Sets the autoScroll option.\r\n        * It changes the scroll bar visibility and the history of the site as a result.\r\n        */\r\n        function setAutoScrolling(value, type){\r\n            //removing the transformation\r\n            if(!value){\r\n                silentScroll(0);\r\n            }\r\n\r\n            setVariableState('autoScrolling', value, type);\r\n\r\n            var element = $(SECTION_ACTIVE_SEL)[0];\r\n\r\n            if(options.autoScrolling && !options.scrollBar){\r\n                css($htmlBody, {\r\n                    'overflow': 'hidden',\r\n                    'height': '100%'\r\n                });\r\n\r\n                setRecordHistory(originals.recordHistory, 'internal');\r\n\r\n                //for IE touch devices\r\n                css(container, {\r\n                    '-ms-touch-action': 'none',\r\n                    'touch-action': 'none'\r\n                });\r\n\r\n                if(element != null){\r\n                    //moving the container up\r\n                    silentScroll(element.offsetTop);\r\n                }\r\n            }else{\r\n                css($htmlBody, {\r\n                    'overflow' : 'visible',\r\n                    'height' : 'initial'\r\n                });\r\n\r\n                var recordHistory = !options.autoScrolling ? false : originals.recordHistory;\r\n                setRecordHistory(recordHistory, 'internal');\r\n\r\n                //for IE touch devices\r\n                css(container, {\r\n                    '-ms-touch-action': '',\r\n                    'touch-action': ''\r\n                });\r\n\r\n                //scrolling the page to the section with no animation\r\n                if (element != null) {\r\n                    var scrollSettings = getScrollSettings(element.offsetTop);\r\n                    scrollSettings.element.scrollTo(0, scrollSettings.options);\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Defines wheter to record the history for each hash change in the URL.\r\n        */\r\n        function setRecordHistory(value, type){\r\n            setVariableState('recordHistory', value, type);\r\n        }\r\n\r\n        /**\r\n        * Defines the scrolling speed\r\n        */\r\n        function setScrollingSpeed(value, type){\r\n            setVariableState('scrollingSpeed', value, type);\r\n        }\r\n\r\n        /**\r\n        * Sets fitToSection\r\n        */\r\n        function setFitToSection(value, type){\r\n            setVariableState('fitToSection', value, type);\r\n        }\r\n\r\n        /**\r\n        * Sets lockAnchors\r\n        */\r\n        function setLockAnchors(value){\r\n            options.lockAnchors = value;\r\n        }\r\n\r\n        /**\r\n        * Adds or remove the possibility of scrolling through sections by using the mouse wheel or the trackpad.\r\n        */\r\n        function setMouseWheelScrolling(value){\r\n            if(value){\r\n                addMouseWheelHandler();\r\n                addMiddleWheelHandler();\r\n            }else{\r\n                removeMouseWheelHandler();\r\n                removeMiddleWheelHandler();\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Adds or remove the possibility of scrolling through sections by using the mouse wheel/trackpad or touch gestures.\r\n        * Optionally a second parameter can be used to specify the direction for which the action will be applied.\r\n        *\r\n        * @param directions string containing the direction or directions separated by comma.\r\n        */\r\n        function setAllowScrolling(value, directions){\r\n            if(typeof directions !== 'undefined'){\r\n                directions = directions.replace(/ /g,'').split(',');\r\n\r\n                directions.forEach(function (direction){\r\n                    setIsScrollAllowed(value, direction, 'm');\r\n                });\r\n            }\r\n            else{\r\n                setIsScrollAllowed(value, 'all', 'm');\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Adds or remove the mouse wheel hijacking\r\n        */\r\n        function setMouseHijack(value){\r\n            if(value){\r\n                setMouseWheelScrolling(true);\r\n                addTouchHandler();\r\n            }else{\r\n                setMouseWheelScrolling(false);\r\n                removeTouchHandler();\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Adds or remove the possibility of scrolling through sections by using the keyboard arrow keys\r\n        */\r\n        function setKeyboardScrolling(value, directions){\r\n            if(typeof directions !== 'undefined'){\r\n                directions = directions.replace(/ /g,'').split(',');\r\n\r\n                directions.forEach(function(direction){\r\n                    setIsScrollAllowed(value, direction, 'k');\r\n                });\r\n            }else{\r\n                setIsScrollAllowed(value, 'all', 'k');\r\n                options.keyboardScrolling = value;\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Moves the page up one section.\r\n        */\r\n        function moveSectionUp(){\r\n            var prev = prevUntil($(SECTION_ACTIVE_SEL)[0], SECTION_SEL);\r\n\r\n            //looping to the bottom if there's no more sections above\r\n            if (!prev && (options.loopTop || options.continuousVertical)) {\r\n                prev = last($(SECTION_SEL));\r\n            }\r\n\r\n            if (prev != null) {\r\n                scrollPage(prev, null, true);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Moves the page down one section.\r\n        */\r\n        function moveSectionDown(){\r\n            var next = nextUntil($(SECTION_ACTIVE_SEL)[0], SECTION_SEL);\r\n\r\n            //looping to the top if there's no more sections below\r\n            if(!next &&\r\n                (options.loopBottom || options.continuousVertical)){\r\n                next = $(SECTION_SEL)[0];\r\n            }\r\n\r\n            if(next != null){\r\n                scrollPage(next, null, false);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Moves the page to the given section and slide with no animation.\r\n        * Anchors or index positions can be used as params.\r\n        */\r\n        function silentMoveTo(sectionAnchor, slideAnchor){\r\n            setScrollingSpeed (0, 'internal');\r\n            moveTo(sectionAnchor, slideAnchor);\r\n            setScrollingSpeed (originals.scrollingSpeed, 'internal');\r\n        }\r\n\r\n        /**\r\n        * Moves the page to the given section and slide.\r\n        * Anchors or index positions can be used as params.\r\n        */\r\n        function moveTo(sectionAnchor, slideAnchor){\r\n            var destiny = getSectionByAnchor(sectionAnchor);\r\n\r\n            if (typeof slideAnchor !== 'undefined'){\r\n                scrollPageAndSlide(sectionAnchor, slideAnchor);\r\n            }else if(destiny != null){\r\n                scrollPage(destiny);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Slides right the slider of the active section.\r\n        * Optional `section` param.\r\n        */\r\n        function moveSlideRight(section){\r\n            moveSlide('right', section);\r\n        }\r\n\r\n        /**\r\n        * Slides left the slider of the active section.\r\n        * Optional `section` param.\r\n        */\r\n        function moveSlideLeft(section){\r\n            moveSlide('left', section);\r\n        }\r\n\r\n        /**\r\n         * When resizing is finished, we adjust the slides sizes and positions\r\n         */\r\n        function reBuild(resizing){\r\n            if(hasClass(container, DESTROYED)){ return; }  //nothing to do if the plugin was destroyed\r\n\r\n            isResizing = true;\r\n\r\n            //updating global vars\r\n            windowsHeight = getWindowHeight();\r\n            windowsWidth = getWindowWidth();\r\n\r\n            var sections = $(SECTION_SEL);\r\n            for (var i = 0; i < sections.length; ++i) {\r\n                var section = sections[i];\r\n                var slidesWrap = $(SLIDES_WRAPPER_SEL, section)[0];\r\n                var slides = $(SLIDE_SEL, section);\r\n\r\n                //adjusting the height of the table-cell for IE and Firefox\r\n                if(options.verticalCentered){\r\n                    css($(TABLE_CELL_SEL, section), {'height': getTableHeight(section) + 'px'});\r\n                }\r\n\r\n                css(section, {'height': windowsHeight + 'px'});\r\n\r\n                //adjusting the position fo the FULL WIDTH slides...\r\n                if (slides.length > 1) {\r\n                    landscapeScroll(slidesWrap, $(SLIDE_ACTIVE_SEL, slidesWrap)[0]);\r\n                }\r\n            }\r\n\r\n            if(options.scrollOverflow){\r\n                scrollBarHandler.createScrollBarForAll();\r\n            }\r\n\r\n            var activeSection = $(SECTION_ACTIVE_SEL)[0];\r\n            var sectionIndex = index(activeSection, SECTION_SEL);\r\n\r\n            //isn't it the first section?\r\n            if(sectionIndex){\r\n                //adjusting the position for the current section\r\n                silentMoveTo(sectionIndex + 1);\r\n            }\r\n\r\n            isResizing = false;\r\n            if(isFunction( options.afterResize ) && resizing){\r\n                options.afterResize.call(container, window.innerWidth, window.innerHeight);\r\n            }\r\n            if(isFunction( options.afterReBuild ) && !resizing){\r\n                options.afterReBuild.call(container);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Determines whether fullpage.js is in responsive mode or not.\r\n        */\r\n        function isResponsiveMode(){\r\n           return hasClass($body, RESPONSIVE);\r\n        }\r\n\r\n        /**\r\n        * Turns fullPage.js to normal scrolling mode when the viewport `width` or `height`\r\n        * are smaller than the set limit values.\r\n        */\r\n        function setResponsive(active){\r\n            var isResponsive = isResponsiveMode();\r\n\r\n            if(active){\r\n                if(!isResponsive){\r\n                    setAutoScrolling(false, 'internal');\r\n                    setFitToSection(false, 'internal');\r\n                    hide($(SECTION_NAV_SEL));\r\n                    addClass($body, RESPONSIVE);\r\n                    if(isFunction( options.afterResponsive )){\r\n                        options.afterResponsive.call( container, active);\r\n                    }\r\n\r\n                    //when on page load, we will remove scrolloverflow if necessary\r\n                    if(options.scrollOverflow){\r\n                        scrollBarHandler.createScrollBarForAll();\r\n                    }\r\n                }\r\n            }\r\n            else if(isResponsive){\r\n                setAutoScrolling(originals.autoScrolling, 'internal');\r\n                setFitToSection(originals.autoScrolling, 'internal');\r\n                show($(SECTION_NAV_SEL));\r\n                removeClass($body, RESPONSIVE);\r\n                if(isFunction( options.afterResponsive )){\r\n                    options.afterResponsive.call( container, active);\r\n                }\r\n            }\r\n        }\r\n\r\n        if(container){\r\n            //public functions\r\n            FP.version = '3.0.5';\r\n            FP.setAutoScrolling = setAutoScrolling;\r\n            FP.setRecordHistory = setRecordHistory;\r\n            FP.setScrollingSpeed = setScrollingSpeed;\r\n            FP.setFitToSection = setFitToSection;\r\n            FP.setLockAnchors = setLockAnchors;\r\n            FP.setMouseWheelScrolling = setMouseWheelScrolling;\r\n            FP.setAllowScrolling = setAllowScrolling;\r\n            FP.setKeyboardScrolling = setKeyboardScrolling;\r\n            FP.moveSectionUp = moveSectionUp;\r\n            FP.moveSectionDown = moveSectionDown;\r\n            FP.silentMoveTo = silentMoveTo;\r\n            FP.moveTo = moveTo;\r\n            FP.moveSlideRight = moveSlideRight;\r\n            FP.moveSlideLeft = moveSlideLeft;\r\n            FP.fitToSection = fitToSection;\r\n            FP.reBuild = reBuild;\r\n            FP.setResponsive = setResponsive;\r\n            FP.getFullpageData = function(){ return options };\r\n            FP.destroy = destroy;\r\n            FP.getActiveSection = getActiveSection;\r\n            FP.getActiveSlide = getActiveSlide;\r\n\r\n            FP.test = {\r\n                top: '0px',\r\n                translate3d: 'translate3d(0px, 0px, 0px)',\r\n                translate3dH: (function(){\r\n                    var a = [];\r\n                    for(var i = 0; i < $(options.sectionSelector, container).length; i++){\r\n                        a.push('translate3d(0px, 0px, 0px)');\r\n                    }\r\n                    return a;\r\n                })(),\r\n                left: (function(){\r\n                    var a = [];\r\n                    for(var i = 0; i < $(options.sectionSelector, container).length; i++){\r\n                        a.push(0);\r\n                    }\r\n                    return a;\r\n                })(),\r\n                options: options,\r\n                setAutoScrolling: setAutoScrolling\r\n            };\r\n\r\n            //functions we want to share across files but which are not\r\n            //mean to be used on their own by developers\r\n            FP.shared = {\r\n                afterRenderActions: afterRenderActions,\r\n                isNormalScrollElement: false\r\n            };\r\n\r\n            window.fullpage_api = FP;\r\n\r\n            //using jQuery initialization? Creating the $.fn.fullpage object\r\n            if(options.$){\r\n                Object.keys(FP).forEach(function (key) {    \r\n                    options.$.fn.fullpage[key] = FP[key];   \r\n                });\r\n            }\r\n\r\n            init();\r\n\r\n            bindEvents();\r\n        }\r\n\r\n        function init(){\r\n            //if css3 is not supported, it will use jQuery animations\r\n            if(options.css3){\r\n                options.css3 = support3d();\r\n            }\r\n\r\n            options.scrollBar = options.scrollBar || options.hybrid;\r\n\r\n            setOptionsFromDOM();\r\n            prepareDom();\r\n            setAllowScrolling(true);\r\n            setMouseHijack(true);\r\n            setAutoScrolling(options.autoScrolling, 'internal');\r\n            responsive();\r\n\r\n            //setting the class for the body element\r\n            setBodyClass();\r\n\r\n            if(document.readyState === 'complete'){\r\n                scrollToAnchor();\r\n            }\r\n            window.addEventListener('load', scrollToAnchor);\r\n\r\n            //if we use scrollOverflow we'll fire afterRender in the scrolloverflow file\r\n            if(!options.scrollOverflow){\r\n                afterRenderActions();\r\n            }\r\n\r\n            doubleCheckHeight();\r\n        }\r\n\r\n        function bindEvents(){\r\n\r\n            //when scrolling...\r\n            window.addEventListener('scroll', scrollHandler);\r\n\r\n            //detecting any change on the URL to scroll to the given anchor link\r\n            //(a way to detect back history button as we play with the hashes on the URL)\r\n            window.addEventListener('hashchange', hashChangeHandler);\r\n\r\n            //when opening a new tab (ctrl + t), `control` won't be pressed when coming back.\r\n            window.addEventListener('blur', blurHandler);\r\n\r\n            //when resizing the site, we adjust the heights of the sections, slimScroll...\r\n            window.addEventListener('resize', resizeHandler);\r\n\r\n            //Sliding with arrow keys, both, vertical and horizontal\r\n            document.addEventListener('keydown', keydownHandler);\r\n\r\n            //to prevent scrolling while zooming\r\n            document.addEventListener('keyup', keyUpHandler);\r\n\r\n            //Scrolls to the section when clicking the navigation bullet\r\n            //simulating the jQuery .on('click') event using delegation\r\n            ['click', 'touchstart'].forEach(function(eventName){\r\n                document.addEventListener(eventName, delegatedEvents);\r\n            });\r\n\r\n            /**\r\n            * Applying normalScroll elements.\r\n            * Ignoring the scrolls over the specified selectors.\r\n            */\r\n            if(options.normalScrollElements){\r\n                ['mouseenter', 'touchstart'].forEach(function(eventName){\r\n                    forMouseLeaveOrTouch(eventName, false);\r\n                });\r\n\r\n                ['mouseleave', 'touchend'].forEach(function(eventName){\r\n                   forMouseLeaveOrTouch(eventName, true);\r\n                });\r\n            }\r\n        }\r\n\r\n        function delegatedEvents(e){\r\n            var target = e.target;\r\n\r\n            if(target && closest(target, SECTION_NAV_SEL + ' a')){\r\n                sectionBulletHandler.call(target, e);\r\n            }\r\n            else if(matches(target, SECTION_NAV_TOOLTIP_SEL)){\r\n                tooltipTextHandler.call(target);\r\n            }\r\n            else if(matches(target, SLIDES_ARROW_SEL)){\r\n                slideArrowHandler.call(target, e);\r\n            }\r\n            else if(matches(target, SLIDES_NAV_LINK_SEL) || closest(target, SLIDES_NAV_LINK_SEL) != null){\r\n                slideBulletHandler.call(target, e);\r\n            }\r\n            else if(closest(target, options.menu + ' [data-menuanchor]')){\r\n                menuItemsHandler.call(target, e);\r\n            }\r\n        }\r\n\r\n        function forMouseLeaveOrTouch(eventName, allowScrolling){\r\n            //a way to pass arguments to the onMouseEnterOrLeave function\r\n            document['fp_' + eventName] = allowScrolling;\r\n            document.addEventListener(eventName, onMouseEnterOrLeave, true); //capturing phase\r\n        }\r\n\r\n        function onMouseEnterOrLeave(e) {\r\n            //onMouseLeave will use the destination target, not the one we are moving away from\r\n            var target = event.toElement || e.relatedTarget || e.target;\r\n\r\n            var type = e.type;\r\n            var isInsideOneNormalScroll = false;\r\n\r\n            //coming from closing a normalScrollElements modal or moving outside viewport?\r\n            if(target == document || !target){\r\n                setMouseHijack(true);\r\n                return;\r\n            }\r\n\r\n            if(type === 'touchend'){\r\n                g_canFireMouseEnterNormalScroll = false;\r\n                setTimeout(function(){\r\n                    g_canFireMouseEnterNormalScroll = true;\r\n                }, 800);\r\n            }\r\n\r\n            //preventing mouseenter event to do anything when coming from a touchEnd event\r\n            //fixing issue #3576\r\n            if(type === 'mouseenter' && !g_canFireMouseEnterNormalScroll){\r\n                return;\r\n            }\r\n\r\n            var normalSelectors = options.normalScrollElements.split(',');\r\n\r\n            normalSelectors.forEach(function(normalSelector){\r\n                if(!isInsideOneNormalScroll){\r\n                    var isNormalScrollTarget = matches(target, normalSelector);\r\n\r\n                    //leaving a child inside the normalScoll element is not leaving the normalScroll #3661\r\n                    var isNormalScrollChildFocused = closest(target, normalSelector);\r\n\r\n                    if(isNormalScrollTarget || isNormalScrollChildFocused){\r\n                        if(!FP.shared.isNormalScrollElement){\r\n                            setMouseHijack(false);\r\n                        }\r\n                        FP.shared.isNormalScrollElement = true;\r\n                        isInsideOneNormalScroll = true;\r\n                    }\r\n                }\r\n            });\r\n\r\n            //not inside a single normal scroll element anymore?\r\n            if(!isInsideOneNormalScroll && FP.shared.isNormalScrollElement){\r\n                setMouseHijack(true);\r\n                FP.shared.isNormalScrollElement = false;\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Checks the viewport a few times on a define interval of time to \r\n        * see if it has changed in any of those. If that's the case, it resizes.\r\n        */\r\n        function doubleCheckHeight(){\r\n            for(var i = 1; i < 4; i++){\r\n                g_doubleCheckHeightId = setTimeout(adjustToNewViewport, 350 * i);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Adjusts a section to the viewport if it has changed.\r\n        */\r\n        function adjustToNewViewport(){\r\n            var newWindowHeight = getWindowHeight();\r\n            var newWindowWidth = getWindowWidth();\r\n\r\n            if(windowsHeight !== newWindowHeight || windowsWidth !== newWindowWidth){\r\n                windowsHeight = newWindowHeight;\r\n                windowsWidth = newWindowWidth;\r\n                reBuild(true);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Setting options from DOM elements if they are not provided.\r\n        */\r\n        function setOptionsFromDOM(){\r\n\r\n            //no anchors option? Checking for them in the DOM attributes\r\n            if(!options.anchors.length){\r\n                var attrName = '[data-anchor]';\r\n                var anchors = $(options.sectionSelector.split(',').join(attrName + ',') + attrName, container);\r\n                if(anchors.length){\r\n                    g_initialAnchorsInDom = true;\r\n                    anchors.forEach(function(item){\r\n                        options.anchors.push(item.getAttribute('data-anchor').toString());\r\n                    });\r\n                }\r\n            }\r\n\r\n            //no tooltips option? Checking for them in the DOM attributes\r\n            if(!options.navigationTooltips.length){\r\n                var attrName = '[data-tooltip]';\r\n                var tooltips = $(options.sectionSelector.split(',').join(attrName + ',') + attrName, container);\r\n                if(tooltips.length){\r\n                    tooltips.forEach(function(item){\r\n                        options.navigationTooltips.push(item.getAttribute('data-tooltip').toString());\r\n                    });\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Works over the DOM structure to set it up for the current fullpage options.\r\n        */\r\n        function prepareDom(){\r\n            css(container, {\r\n                'height': '100%',\r\n                'position': 'relative'\r\n            });\r\n\r\n            //adding a class to recognize the container internally in the code\r\n            addClass(container, WRAPPER);\r\n            addClass($html, ENABLED);\r\n\r\n            //due to https://github.com/alvarotrigo/fullPage.js/issues/1502\r\n            windowsHeight = getWindowHeight();\r\n\r\n            removeClass(container, DESTROYED); //in case it was destroyed before initializing it again\r\n\r\n            addInternalSelectors();\r\n\r\n            var sections = $(SECTION_SEL);\r\n\r\n            //styling the sections / slides / menu\r\n            for(var i = 0; i<sections.length; i++){\r\n                var sectionIndex = i;\r\n                var section = sections[i];\r\n                var slides = $(SLIDE_SEL, section);\r\n                var numSlides = slides.length;\r\n\r\n                //caching the original styles to add them back on destroy('all')\r\n                section.setAttribute('data-fp-styles', section.getAttribute('style'));\r\n\r\n                styleSection(section, sectionIndex);\r\n                styleMenu(section, sectionIndex);\r\n\r\n                // if there's any slide\r\n                if (numSlides > 0) {\r\n                    styleSlides(section, slides, numSlides);\r\n                }else{\r\n                    if(options.verticalCentered){\r\n                        addTableClass(section);\r\n                    }\r\n                }\r\n            }\r\n\r\n            //fixed elements need to be moved out of the plugin container due to problems with CSS3.\r\n            if(options.fixedElements && options.css3){\r\n                $(options.fixedElements).forEach(function(item){\r\n                    $body.appendChild(item);\r\n                });\r\n            }\r\n\r\n            //vertical centered of the navigation + active bullet\r\n            if(options.navigation){\r\n                addVerticalNavigation();\r\n            }\r\n\r\n            enableYoutubeAPI();\r\n\r\n            if(options.scrollOverflow){\r\n                scrollBarHandler = options.scrollOverflowHandler.init(options);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Styles the horizontal slides for a section.\r\n        */\r\n        function styleSlides(section, slides, numSlides){\r\n            var sliderWidth = numSlides * 100;\r\n            var slideWidth = 100 / numSlides;\r\n\r\n            var slidesWrapper = document.createElement('div');\r\n            slidesWrapper.className = SLIDES_WRAPPER; //fp-slides\r\n            wrapAll(slides, slidesWrapper);\r\n\r\n            var slidesContainer = document.createElement('div');\r\n            slidesContainer.className = SLIDES_CONTAINER; //fp-slidesContainer\r\n            wrapAll(slides, slidesContainer);\r\n\r\n            css($(SLIDES_CONTAINER_SEL, section), {'width': sliderWidth + '%'});\r\n\r\n            if(numSlides > 1){\r\n                if(options.controlArrows){\r\n                    createSlideArrows(section);\r\n                }\r\n\r\n                if(options.slidesNavigation){\r\n                    addSlidesNavigation(section, numSlides);\r\n                }\r\n            }\r\n\r\n            slides.forEach(function(slide) {\r\n                css(slide, {'width': slideWidth + '%'});\r\n\r\n                if(options.verticalCentered){\r\n                    addTableClass(slide);\r\n                }\r\n            });\r\n\r\n            var startingSlide = $(SLIDE_ACTIVE_SEL, section)[0];\r\n\r\n            //if the slide won't be an starting point, the default will be the first one\r\n            //the active section isn't the first one? Is not the first slide of the first section? Then we load that section/slide by default.\r\n            if( startingSlide != null && (index($(SECTION_ACTIVE_SEL), SECTION_SEL) !== 0 || (index($(SECTION_ACTIVE_SEL), SECTION_SEL) === 0 && index(startingSlide) !== 0))){\r\n                silentLandscapeScroll(startingSlide, 'internal');\r\n            }else{\r\n                addClass(slides[0], ACTIVE);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Styling vertical sections\r\n        */\r\n        function styleSection(section, index){\r\n            //if no active section is defined, the 1st one will be the default one\r\n            if(!index && $(SECTION_ACTIVE_SEL)[0] == null) {\r\n                addClass(section, ACTIVE);\r\n            }\r\n            startingSection = $(SECTION_ACTIVE_SEL)[0];\r\n\r\n            css(section, {'height': windowsHeight + 'px'});\r\n\r\n            if(options.paddingTop){\r\n                css(section, {'padding-top': options.paddingTop});\r\n            }\r\n\r\n            if(options.paddingBottom){\r\n                css(section, {'padding-bottom': options.paddingBottom});\r\n            }\r\n\r\n            if (typeof options.sectionsColor[index] !==  'undefined') {\r\n                css(section, {'background-color': options.sectionsColor[index]});\r\n            }\r\n\r\n            if (typeof options.anchors[index] !== 'undefined') {\r\n                section.setAttribute('data-anchor', options.anchors[index]);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Sets the data-anchor attributes to the menu elements and activates the current one.\r\n        */\r\n        function styleMenu(section, index){\r\n            if (typeof options.anchors[index] !== 'undefined') {\r\n                //activating the menu / nav element on load\r\n                if(hasClass(section, ACTIVE)){\r\n                    activateMenuAndNav(options.anchors[index], index);\r\n                }\r\n            }\r\n\r\n            //moving the menu outside the main container if it is inside (avoid problems with fixed positions when using CSS3 tranforms)\r\n            if(options.menu && options.css3 && closest($(options.menu)[0], WRAPPER_SEL) != null){\r\n                $(options.menu).forEach(function(menu) {\r\n                    $body.appendChild(menu);\r\n                });\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Adds internal classes to be able to provide customizable selectors\r\n        * keeping the link with the style sheet.\r\n        */\r\n        function addInternalSelectors(){\r\n            addClass($(options.sectionSelector, container), SECTION);\r\n            addClass($(options.slideSelector, container), SLIDE);\r\n        }\r\n\r\n        /**\r\n        * Creates the control arrows for the given section\r\n        */\r\n        function createSlideArrows(section){\r\n            var arrows = [createElementFromHTML('<div class=\"' + SLIDES_ARROW_PREV + '\"></div>'), createElementFromHTML('<div class=\"' + SLIDES_ARROW_NEXT + '\"></div>')];\r\n            after($(SLIDES_WRAPPER_SEL, section)[0], arrows);\r\n\r\n            if(options.controlArrowColor !== '#fff'){\r\n                css($(SLIDES_ARROW_NEXT_SEL, section), {'border-color': 'transparent transparent transparent '+options.controlArrowColor});\r\n                css($(SLIDES_ARROW_PREV_SEL, section), {'border-color': 'transparent '+ options.controlArrowColor + ' transparent transparent'});\r\n            }\r\n\r\n            if(!options.loopHorizontal){\r\n                hide($(SLIDES_ARROW_PREV_SEL, section));\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Creates a vertical navigation bar.\r\n        */\r\n        function addVerticalNavigation(){\r\n            var navigation = document.createElement('div');\r\n            navigation.setAttribute('id', SECTION_NAV);\r\n\r\n            var divUl = document.createElement('ul');\r\n            navigation.appendChild(divUl);\r\n\r\n            appendTo(navigation, $body);\r\n            var nav = $(SECTION_NAV_SEL)[0];\r\n\r\n            addClass(nav, 'fp-' + options.navigationPosition);\r\n\r\n            if(options.showActiveTooltip){\r\n                addClass(nav, SHOW_ACTIVE_TOOLTIP);\r\n            }\r\n\r\n            var li = '';\r\n\r\n            for (var i = 0; i < $(SECTION_SEL).length; i++) {\r\n                var link = '';\r\n                if (options.anchors.length) {\r\n                    link = options.anchors[i];\r\n                }\r\n\r\n                li += '<li><a href=\"#' + link + '\"><span class=\"fp-sr-only\">' + getBulletLinkName(i, 'Section') + '</span><span></span></a>';\r\n\r\n                // Only add tooltip if needed (defined by user)\r\n                var tooltip = options.navigationTooltips[i];\r\n\r\n                if (typeof tooltip !== 'undefined' && tooltip !== '') {\r\n                    li += '<div class=\"' + SECTION_NAV_TOOLTIP + ' fp-' + options.navigationPosition + '\">' + tooltip + '</div>';\r\n                }\r\n\r\n                li += '</li>';\r\n            }\r\n            $('ul', nav)[0].innerHTML = li;\r\n\r\n            //centering it vertically\r\n            css($(SECTION_NAV_SEL), {'margin-top': '-' + ($(SECTION_NAV_SEL)[0].offsetHeight/2) + 'px'});\r\n\r\n            //activating the current active section\r\n\r\n            var bullet = $('li', $(SECTION_NAV_SEL)[0])[index($(SECTION_ACTIVE_SEL)[0], SECTION_SEL)];\r\n            addClass($('a', bullet), ACTIVE);\r\n        }\r\n\r\n        /**\r\n        * Gets the name for screen readers for a section/slide navigation bullet.\r\n        */\r\n        function getBulletLinkName(i, defaultName){\r\n            return options.navigationTooltips[i]\r\n                || options.anchors[i]\r\n                || defaultName + ' ' + (i+1)\r\n        }\r\n\r\n        /*\r\n        * Enables the Youtube videos API so we can control their flow if necessary.\r\n        */\r\n        function enableYoutubeAPI(){\r\n            $('iframe[src*=\"youtube.com/embed/\"]', container).forEach(function(item){\r\n                addURLParam(item, 'enablejsapi=1');\r\n            });\r\n        }\r\n\r\n        /**\r\n        * Adds a new parameter and its value to the `src` of a given element\r\n        */\r\n        function addURLParam(element, newParam){\r\n            var originalSrc = element.getAttribute('src');\r\n            element.setAttribute('src', originalSrc + getUrlParamSign(originalSrc) + newParam);\r\n        }\r\n\r\n        /*\r\n        * Returns the prefix sign to use for a new parameter in an existen URL.\r\n        *\r\n        * @return {String}  ? | &\r\n        */\r\n        function getUrlParamSign(url){\r\n            return ( !/\\?/.test( url ) ) ? '?' : '&';\r\n        }\r\n\r\n        /**\r\n        * Actions and callbacks to fire afterRender\r\n        */\r\n        function afterRenderActions(){\r\n            var section = $(SECTION_ACTIVE_SEL)[0];\r\n\r\n            addClass(section, COMPLETELY);\r\n\r\n            lazyLoad(section);\r\n            lazyLoadOthers();\r\n            playMedia(section);\r\n\r\n            if(options.scrollOverflow){\r\n                options.scrollOverflowHandler.afterLoad();\r\n            }\r\n\r\n            if(isDestinyTheStartingSection() && isFunction(options.afterLoad) ){\r\n                fireCallback('afterLoad', {\r\n                    activeSection: section,\r\n                    element: section,\r\n                    direction: null,\r\n\r\n                    //for backwards compatibility callback (to be removed in a future!)\r\n                    anchorLink: section.getAttribute('data-anchor'),\r\n                    sectionIndex: index(section, SECTION_SEL)\r\n                });\r\n            }\r\n\r\n            if(isFunction(options.afterRender)){\r\n                fireCallback('afterRender');\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Determines if the URL anchor destiny is the starting section (the one using 'active' class before initialization)\r\n        */\r\n        function isDestinyTheStartingSection(){\r\n            var anchor = getAnchorsURL();\r\n            var destinationSection = getSectionByAnchor(anchor.section);\r\n            return !anchor.section || !destinationSection || typeof destinationSection !=='undefined' && index(destinationSection) === index(startingSection);\r\n        }\r\n\r\n        var isScrolling = false;\r\n        var lastScroll = 0;\r\n\r\n        //when scrolling...\r\n        function scrollHandler(){\r\n            var currentSection;\r\n\r\n            if(!options.autoScrolling || options.scrollBar){\r\n                var currentScroll = getScrollTop();\r\n                var scrollDirection = getScrollDirection(currentScroll);\r\n                var visibleSectionIndex = 0;\r\n                var screen_mid = currentScroll + (getWindowHeight() / 2.0);\r\n                var isAtBottom = $body.offsetHeight - getWindowHeight() === currentScroll;\r\n                var sections =  $(SECTION_SEL);\r\n\r\n                //when using `auto-height` for a small last section it won't be centered in the viewport\r\n                if(isAtBottom){\r\n                    visibleSectionIndex = sections.length - 1;\r\n                }\r\n                //is at top? when using `auto-height` for a small first section it won't be centered in the viewport\r\n                else if(!currentScroll){\r\n                    visibleSectionIndex = 0;\r\n                }\r\n\r\n                //taking the section which is showing more content in the viewport\r\n                else{\r\n                    for (var i = 0; i < sections.length; ++i) {\r\n                        var section = sections[i];\r\n\r\n                        // Pick the the last section which passes the middle line of the screen.\r\n                        if (section.offsetTop <= screen_mid)\r\n                        {\r\n                            visibleSectionIndex = i;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                if(isCompletelyInViewPort(scrollDirection)){\r\n                    if(!hasClass($(SECTION_ACTIVE_SEL)[0], COMPLETELY)){\r\n                        addClass($(SECTION_ACTIVE_SEL)[0], COMPLETELY);\r\n                        removeClass(siblings($(SECTION_ACTIVE_SEL)[0]), COMPLETELY);\r\n                    }\r\n                }\r\n\r\n                //geting the last one, the current one on the screen\r\n                currentSection = sections[visibleSectionIndex];\r\n\r\n                //setting the visible section as active when manually scrolling\r\n                //executing only once the first time we reach the section\r\n                if(!hasClass(currentSection, ACTIVE)){\r\n                    isScrolling = true;\r\n                    var leavingSection = $(SECTION_ACTIVE_SEL)[0];\r\n                    var leavingSectionIndex = index(leavingSection, SECTION_SEL) + 1;\r\n                    var yMovement = getYmovement(currentSection);\r\n                    var anchorLink  = currentSection.getAttribute('data-anchor');\r\n                    var sectionIndex = index(currentSection, SECTION_SEL) + 1;\r\n                    var activeSlide = $(SLIDE_ACTIVE_SEL, currentSection)[0];\r\n                    var slideIndex;\r\n                    var slideAnchorLink;\r\n                    var callbacksParams = {\r\n                        activeSection: leavingSection,\r\n                        sectionIndex: sectionIndex -1,\r\n                        anchorLink: anchorLink,\r\n                        element: currentSection,\r\n                        leavingSection: leavingSectionIndex,\r\n                        direction: yMovement\r\n                    };\r\n\r\n                    if(activeSlide){\r\n                        slideAnchorLink = activeSlide.getAttribute('data-anchor');\r\n                        slideIndex = index(activeSlide);\r\n                    }\r\n\r\n                    if(canScroll){\r\n                        addClass(currentSection, ACTIVE);\r\n                        removeClass(siblings(currentSection), ACTIVE);\r\n\r\n                        if(isFunction( options.onLeave )){\r\n                            fireCallback('onLeave', callbacksParams);\r\n                        }\r\n                        if(isFunction( options.afterLoad )){\r\n                            fireCallback('afterLoad', callbacksParams);\r\n                        }\r\n\r\n                        stopMedia(leavingSection);\r\n                        lazyLoad(currentSection);\r\n                        playMedia(currentSection);\r\n\r\n                        activateMenuAndNav(anchorLink, sectionIndex - 1);\r\n\r\n                        if(options.anchors.length){\r\n                            //needed to enter in hashChange event when using the menu with anchor links\r\n                            lastScrolledDestiny = anchorLink;\r\n                        }\r\n                        setState(slideIndex, slideAnchorLink, anchorLink, sectionIndex);\r\n                    }\r\n\r\n                    //small timeout in order to avoid entering in hashChange event when scrolling is not finished yet\r\n                    clearTimeout(scrollId);\r\n                    scrollId = setTimeout(function(){\r\n                        isScrolling = false;\r\n                    }, 100);\r\n                }\r\n\r\n                if(options.fitToSection){\r\n                    //for the auto adjust of the viewport to fit a whole section\r\n                    clearTimeout(scrollId2);\r\n\r\n                    scrollId2 = setTimeout(function(){\r\n                        //checking it again in case it changed during the delay\r\n                        if(options.fitToSection &&\r\n\r\n                            //is the destination element bigger than the viewport?\r\n                            $(SECTION_ACTIVE_SEL)[0].offsetHeight <= windowsHeight\r\n                        ){\r\n                            fitToSection();\r\n                        }\r\n                    }, options.fitToSectionDelay);\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Fits the site to the nearest active section\r\n        */\r\n        function fitToSection(){\r\n            //checking fitToSection again in case it was set to false before the timeout delay\r\n            if(canScroll){\r\n                //allows to scroll to an active section and\r\n                //if the section is already active, we prevent firing callbacks\r\n                isResizing = true;\r\n\r\n                scrollPage($(SECTION_ACTIVE_SEL)[0]);\r\n                isResizing = false;\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Determines whether the active section has seen in its whole or not.\r\n        */\r\n        function isCompletelyInViewPort(movement){\r\n            var top = $(SECTION_ACTIVE_SEL)[0].offsetTop;\r\n            var bottom = top + getWindowHeight();\r\n\r\n            if(movement == 'up'){\r\n                return bottom >= (getScrollTop() + getWindowHeight());\r\n            }\r\n            return top <= getScrollTop();\r\n        }\r\n\r\n        /**\r\n        * Determines whether a section is in the viewport or not.\r\n        */\r\n        function isSectionInViewport (el) {\r\n            var rect = el.getBoundingClientRect();\r\n            var top = rect.top;\r\n            var bottom = rect.bottom;\r\n\r\n            //sometimes there's a 1px offset on the bottom of the screen even when the \r\n            //section's height is the window.innerHeight one. I guess because pixels won't allow decimals.\r\n            //using this prevents from lazyLoading the section that is not yet visible \r\n            //(only 1 pixel offset is)\r\n            var pixelOffset = 2;\r\n            \r\n            var isTopInView = top + pixelOffset < windowsHeight && top > 0;\r\n            var isBottomInView = bottom > pixelOffset && bottom < windowsHeight;\r\n\r\n            return isTopInView || isBottomInView;\r\n        }\r\n\r\n        /**\r\n        * Gets the directon of the the scrolling fired by the scroll event.\r\n        */\r\n        function getScrollDirection(currentScroll){\r\n            var direction = currentScroll > lastScroll ? 'down' : 'up';\r\n\r\n            lastScroll = currentScroll;\r\n\r\n            //needed for auto-height sections to determine if we want to scroll to the top or bottom of the destination\r\n            previousDestTop = currentScroll;\r\n\r\n            return direction;\r\n        }\r\n\r\n        /**\r\n        * Determines the way of scrolling up or down:\r\n        * by 'automatically' scrolling a section or by using the default and normal scrolling.\r\n        */\r\n        function scrolling(type){\r\n            if (!isScrollAllowed.m[type]){\r\n                return;\r\n            }\r\n\r\n            var scrollSection = (type === 'down') ? moveSectionDown : moveSectionUp;\r\n\r\n            if(options.scrollOverflow){\r\n                var scrollable = options.scrollOverflowHandler.scrollable($(SECTION_ACTIVE_SEL)[0]);\r\n                var check = (type === 'down') ? 'bottom' : 'top';\r\n\r\n                if(scrollable != null ){\r\n                    //is the scrollbar at the start/end of the scroll?\r\n                    if(options.scrollOverflowHandler.isScrolled(check, scrollable)){\r\n                        scrollSection();\r\n                    }else{\r\n                        return true;\r\n                    }\r\n                }else{\r\n                    // moved up/down\r\n                    scrollSection();\r\n                }\r\n            }else{\r\n                // moved up/down\r\n                scrollSection();\r\n            }\r\n        }\r\n\r\n        /*\r\n        * Preventing bouncing in iOS #2285\r\n        */\r\n        function preventBouncing(e){\r\n            if(options.autoScrolling && isReallyTouch(e) && isScrollAllowed.m.up){\r\n                //preventing the easing on iOS devices\r\n                preventDefault(e);\r\n            }\r\n        }\r\n\r\n        var touchStartY = 0;\r\n        var touchStartX = 0;\r\n        var touchEndY = 0;\r\n        var touchEndX = 0;\r\n\r\n        /* Detecting touch events\r\n\r\n        * As we are changing the top property of the page on scrolling, we can not use the traditional way to detect it.\r\n        * This way, the touchstart and the touch moves shows an small difference between them which is the\r\n        * used one to determine the direction.\r\n        */\r\n        function touchMoveHandler(e){\r\n            var activeSection = closest(e.target, SECTION_SEL) || $(SECTION_ACTIVE_SEL)[0];\r\n\r\n            if (isReallyTouch(e) ) {\r\n\r\n                if(options.autoScrolling){\r\n                    //preventing the easing on iOS devices\r\n                    preventDefault(e);\r\n                }\r\n\r\n                var touchEvents = getEventsPage(e);\r\n\r\n                touchEndY = touchEvents.y;\r\n                touchEndX = touchEvents.x;\r\n\r\n                //if movement in the X axys is greater than in the Y and the currect section has slides...\r\n                if ($(SLIDES_WRAPPER_SEL, activeSection).length && Math.abs(touchStartX - touchEndX) > (Math.abs(touchStartY - touchEndY))) {\r\n\r\n                    //is the movement greater than the minimum resistance to scroll?\r\n                    if (!slideMoving && Math.abs(touchStartX - touchEndX) > (getWindowWidth() / 100 * options.touchSensitivity)) {\r\n                        if (touchStartX > touchEndX) {\r\n                            if(isScrollAllowed.m.right){\r\n                                moveSlideRight(activeSection); //next\r\n                            }\r\n                        } else {\r\n                            if(isScrollAllowed.m.left){\r\n                                moveSlideLeft(activeSection); //prev\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                //vertical scrolling (only when autoScrolling is enabled)\r\n                else if(options.autoScrolling && canScroll){\r\n\r\n                    //is the movement greater than the minimum resistance to scroll?\r\n                    if (Math.abs(touchStartY - touchEndY) > (window.innerHeight / 100 * options.touchSensitivity)) {\r\n                        if (touchStartY > touchEndY) {\r\n                            scrolling('down');\r\n                        } else if (touchEndY > touchStartY) {\r\n                            scrolling('up');\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n        * As IE >= 10 fires both touch and mouse events when using a mouse in a touchscreen\r\n        * this way we make sure that is really a touch event what IE is detecting.\r\n        */\r\n        function isReallyTouch(e){\r\n            //if is not IE   ||  IE is detecting `touch` or `pen`\r\n            return typeof e.pointerType === 'undefined' || e.pointerType != 'mouse';\r\n        }\r\n\r\n        /**\r\n        * Handler for the touch start event.\r\n        */\r\n        function touchStartHandler(e){\r\n\r\n            //stopping the auto scroll to adjust to a section\r\n            if(options.fitToSection){\r\n                activeAnimation = false;\r\n            }\r\n\r\n            if(isReallyTouch(e)){\r\n                var touchEvents = getEventsPage(e);\r\n                touchStartY = touchEvents.y;\r\n                touchStartX = touchEvents.x;\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Gets the average of the last `number` elements of the given array.\r\n        */\r\n        function getAverage(elements, number){\r\n            var sum = 0;\r\n\r\n            //taking `number` elements from the end to make the average, if there are not enought, 1\r\n            var lastElements = elements.slice(Math.max(elements.length - number, 1));\r\n\r\n            for(var i = 0; i < lastElements.length; i++){\r\n                sum = sum + lastElements[i];\r\n            }\r\n\r\n            return Math.ceil(sum/number);\r\n        }\r\n\r\n        /**\r\n         * Detecting mousewheel scrolling\r\n         *\r\n         * http://blogs.sitepointstatic.com/examples/tech/mouse-wheel/index.html\r\n         * http://www.sitepoint.com/html5-javascript-mouse-wheel/\r\n         */\r\n        var prevTime = new Date().getTime();\r\n\r\n        function MouseWheelHandler(e) {\r\n            var curTime = new Date().getTime();\r\n            var isNormalScroll = hasClass($(COMPLETELY_SEL)[0], NORMAL_SCROLL);\r\n\r\n            //is scroll allowed?\r\n            if (!isScrollAllowed.m.down && !isScrollAllowed.m.up) {\r\n                preventDefault(e);\r\n                return false;\r\n            }\r\n\r\n            //autoscrolling and not zooming?\r\n            if(options.autoScrolling && !controlPressed && !isNormalScroll){\r\n                // cross-browser wheel delta\r\n                e = e || window.event;\r\n                var value = e.wheelDelta || -e.deltaY || -e.detail;\r\n                var delta = Math.max(-1, Math.min(1, value));\r\n\r\n                var horizontalDetection = typeof e.wheelDeltaX !== 'undefined' || typeof e.deltaX !== 'undefined';\r\n                var isScrollingVertically = (Math.abs(e.wheelDeltaX) < Math.abs(e.wheelDelta)) || (Math.abs(e.deltaX ) < Math.abs(e.deltaY) || !horizontalDetection);\r\n\r\n                //Limiting the array to 150 (lets not waste memory!)\r\n                if(scrollings.length > 149){\r\n                    scrollings.shift();\r\n                }\r\n\r\n                //keeping record of the previous scrollings\r\n                scrollings.push(Math.abs(value));\r\n\r\n                //preventing to scroll the site on mouse wheel when scrollbar is present\r\n                if(options.scrollBar){\r\n                    preventDefault(e);\r\n                }\r\n\r\n                //time difference between the last scroll and the current one\r\n                var timeDiff = curTime-prevTime;\r\n                prevTime = curTime;\r\n\r\n                //haven't they scrolled in a while?\r\n                //(enough to be consider a different scrolling action to scroll another section)\r\n                if(timeDiff > 200){\r\n                    //emptying the array, we dont care about old scrollings for our averages\r\n                    scrollings = [];\r\n                }\r\n\r\n                if(canScroll){\r\n                    var averageEnd = getAverage(scrollings, 10);\r\n                    var averageMiddle = getAverage(scrollings, 70);\r\n                    var isAccelerating = averageEnd >= averageMiddle;\r\n\r\n                    //to avoid double swipes...\r\n                    if(isAccelerating && isScrollingVertically){\r\n                        //scrolling down?\r\n                        if (delta < 0) {\r\n                            scrolling('down');\r\n\r\n                        //scrolling up?\r\n                        }else {\r\n                            scrolling('up');\r\n                        }\r\n                    }\r\n                }\r\n\r\n                return false;\r\n            }\r\n\r\n            if(options.fitToSection){\r\n                //stopping the auto scroll to adjust to a section\r\n                activeAnimation = false;\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Slides a slider to the given direction.\r\n        * Optional `section` param.\r\n        */\r\n        function moveSlide(direction, section){\r\n            var activeSection = section == null ? $(SECTION_ACTIVE_SEL)[0] : section;\r\n            var slides = $(SLIDES_WRAPPER_SEL, activeSection)[0];\r\n\r\n            // more than one slide needed and nothing should be sliding\r\n            if (slides == null || slideMoving || $(SLIDE_SEL, slides).length < 2) {\r\n                return;\r\n            }\r\n\r\n            var currentSlide = $(SLIDE_ACTIVE_SEL, slides)[0];\r\n            var destiny = null;\r\n\r\n            if(direction === 'left'){\r\n                destiny = prevUntil(currentSlide, SLIDE_SEL);\r\n            }else{\r\n                destiny = nextUntil(currentSlide, SLIDE_SEL);\r\n            }\r\n\r\n            //isn't there a next slide in the secuence?\r\n            if(destiny == null){\r\n                //respect loopHorizontal settin\r\n                if (!options.loopHorizontal) return;\r\n\r\n                var slideSiblings = siblings(currentSlide);\r\n                if(direction === 'left'){\r\n                    destiny = slideSiblings[slideSiblings.length - 1]; //last\r\n                }else{\r\n                    destiny = slideSiblings[0]; //first\r\n                }\r\n            }\r\n\r\n            slideMoving = true && !FP.test.isTesting;\r\n            landscapeScroll(slides, destiny, direction);\r\n        }\r\n\r\n        /**\r\n        * Maintains the active slides in the viewport\r\n        * (Because the `scroll` animation might get lost with some actions, such as when using continuousVertical)\r\n        */\r\n        function keepSlidesPosition(){\r\n            var activeSlides = $(SLIDE_ACTIVE_SEL);\r\n            for( var i =0; i<activeSlides.length; i++){\r\n                silentLandscapeScroll(activeSlides[i], 'internal');\r\n            }\r\n        }\r\n\r\n        var previousDestTop = 0;\r\n        /**\r\n        * Returns the destination Y position based on the scrolling direction and\r\n        * the height of the section.\r\n        */\r\n        function getDestinationPosition(element){\r\n            var elementHeight = element.offsetHeight;\r\n            var elementTop = element.offsetTop;\r\n\r\n            //top of the desination will be at the top of the viewport\r\n            var position = elementTop;\r\n            var isScrollingDown =  elementTop > previousDestTop;\r\n            var sectionBottom = position - windowsHeight + elementHeight;\r\n            var bigSectionsDestination = options.bigSectionsDestination;\r\n\r\n            //is the destination element bigger than the viewport?\r\n            if(elementHeight > windowsHeight){\r\n                //scrolling up?\r\n                if(!isScrollingDown && !bigSectionsDestination || bigSectionsDestination === 'bottom' ){\r\n                    position = sectionBottom;\r\n                }\r\n            }\r\n\r\n            //sections equal or smaller than the viewport height && scrolling down? ||  is resizing and its in the last section\r\n            else if(isScrollingDown || (isResizing && next(element) == null) ){\r\n                //The bottom of the destination will be at the bottom of the viewport\r\n                position = sectionBottom;\r\n            }\r\n\r\n            /*\r\n            Keeping record of the last scrolled position to determine the scrolling direction.\r\n            No conventional methods can be used as the scroll bar might not be present\r\n            AND the section might not be active if it is auto-height and didnt reach the middle\r\n            of the viewport.\r\n            */\r\n            previousDestTop = position;\r\n            return position;\r\n        }\r\n\r\n        /**\r\n        * Scrolls the site to the given element and scrolls to the slide if a callback is given.\r\n        */\r\n        function scrollPage(element, callback, isMovementUp){\r\n            if(element == null){ return; } //there's no element to scroll, leaving the function\r\n\r\n            var dtop = getDestinationPosition(element);\r\n            var slideAnchorLink;\r\n            var slideIndex;\r\n\r\n            //local variables\r\n            var v = {\r\n                element: element,\r\n                callback: callback,\r\n                isMovementUp: isMovementUp,\r\n                dtop: dtop,\r\n                yMovement: getYmovement(element),\r\n                anchorLink: element.getAttribute('data-anchor'),\r\n                sectionIndex: index(element, SECTION_SEL),\r\n                activeSlide: $(SLIDE_ACTIVE_SEL, element)[0],\r\n                activeSection: $(SECTION_ACTIVE_SEL)[0],\r\n                leavingSection: index($(SECTION_ACTIVE_SEL), SECTION_SEL) + 1,\r\n\r\n                //caching the value of isResizing at the momment the function is called\r\n                //because it will be checked later inside a setTimeout and the value might change\r\n                localIsResizing: isResizing\r\n            };\r\n\r\n            //quiting when destination scroll is the same as the current one\r\n            if((v.activeSection == element && !isResizing) || (options.scrollBar && getScrollTop() === v.dtop && !hasClass(element, AUTO_HEIGHT) )){ return; }\r\n\r\n            if(v.activeSlide != null){\r\n                slideAnchorLink = v.activeSlide.getAttribute('data-anchor');\r\n                slideIndex = index(v.activeSlide);\r\n            }\r\n\r\n            //callback (onLeave) if the site is not just resizing and readjusting the slides\r\n            if(!v.localIsResizing){\r\n                var direction = v.yMovement;\r\n\r\n                //required for continousVertical\r\n                if(typeof isMovementUp !== 'undefined'){\r\n                    direction = isMovementUp ? 'up' : 'down';\r\n                }\r\n\r\n                //for the callback\r\n                v.direction = direction;\r\n\r\n                if(isFunction(options.onLeave)){\r\n                    if(fireCallback('onLeave', v) === false){\r\n                        return;\r\n                    }\r\n                }\r\n            }\r\n\r\n            // If continuousVertical && we need to wrap around\r\n            if (options.autoScrolling && options.continuousVertical && typeof (v.isMovementUp) !== \"undefined\" &&\r\n                ((!v.isMovementUp && v.yMovement == 'up') || // Intending to scroll down but about to go up or\r\n                (v.isMovementUp && v.yMovement == 'down'))) { // intending to scroll up but about to go down\r\n\r\n                v = createInfiniteSections(v);\r\n            }\r\n\r\n            //pausing media of the leaving section (if we are not just resizing, as destinatino will be the same one)\r\n            if(!v.localIsResizing){\r\n                stopMedia(v.activeSection);\r\n            }\r\n\r\n            if(options.scrollOverflow){\r\n                options.scrollOverflowHandler.beforeLeave();\r\n            }\r\n\r\n            addClass(element, ACTIVE);\r\n            removeClass(siblings(element), ACTIVE);\r\n            lazyLoad(element);\r\n\r\n            if(options.scrollOverflow){\r\n                options.scrollOverflowHandler.onLeave();\r\n            }\r\n\r\n            //preventing from activating the MouseWheelHandler event\r\n            //more than once if the page is scrolling\r\n            canScroll = false || FP.test.isTesting;\r\n\r\n            setState(slideIndex, slideAnchorLink, v.anchorLink, v.sectionIndex);\r\n\r\n            performMovement(v);\r\n\r\n            //flag to avoid callingn `scrollPage()` twice in case of using anchor links\r\n            lastScrolledDestiny = v.anchorLink;\r\n\r\n            //avoid firing it twice (as it does also on scroll)\r\n            activateMenuAndNav(v.anchorLink, v.sectionIndex);\r\n        }\r\n\r\n        /**\r\n        * Dispatch events & callbacks making sure it does it on the right format, depending on\r\n        * whether v2compatible is being used or not.\r\n        */\r\n        function fireCallback(eventName, v){\r\n            var eventData = getEventData(eventName, v);\r\n\r\n            if(!options.v2compatible){\r\n                trigger(container, eventName, eventData);\r\n\r\n                if(options[eventName].apply(eventData[Object.keys(eventData)[0]], toArray(eventData)) === false){\r\n                    return false;\r\n                }\r\n            }\r\n            else{\r\n                if(options[eventName].apply(eventData[0], eventData.slice(1)) === false){\r\n                    return false;\r\n                }\r\n            }\r\n\r\n            return true;\r\n        }\r\n\r\n        /**\r\n        * Makes sure to only create a Panel object if the element exist\r\n        */\r\n        function nullOrSection(el){\r\n            return el ? new Section(el) : null;\r\n        }\r\n\r\n        function nullOrSlide(el){\r\n            return el ? new Slide(el) : null;\r\n        }\r\n\r\n        /**\r\n        * Gets the event's data for the given event on the right format. Depending on whether\r\n        * v2compatible is being used or not.\r\n        */\r\n        function getEventData(eventName, v){\r\n            var paramsPerEvent;\r\n\r\n            if(!options.v2compatible){\r\n\r\n                //using functions to run only the necessary bits within the object\r\n                paramsPerEvent = {\r\n                    afterRender: function(){\r\n                        return {\r\n                            section: nullOrSection($(SECTION_ACTIVE_SEL)[0]),\r\n                            slide: nullOrSlide($(SLIDE_ACTIVE_SEL, $(SECTION_ACTIVE_SEL)[0])[0])\r\n                        };\r\n                    },\r\n                    onLeave: function(){\r\n                        return {\r\n                            origin: nullOrSection(v.activeSection),\r\n                            destination: nullOrSection(v.element),\r\n                            direction: v.direction\r\n                        };\r\n                    },\r\n\r\n                    afterLoad: function(){\r\n                        return paramsPerEvent.onLeave();\r\n                    },\r\n\r\n                    afterSlideLoad: function(){\r\n                        return {\r\n                            section: nullOrSection(v.section),\r\n                            origin: nullOrSlide(v.prevSlide),\r\n                            destination: nullOrSlide(v.destiny),\r\n                            direction: v.direction\r\n                        };\r\n                    },\r\n\r\n                    onSlideLeave: function(){\r\n                        return paramsPerEvent.afterSlideLoad();\r\n                    }\r\n                };\r\n            }\r\n            else{\r\n                paramsPerEvent = {\r\n                    afterRender: function(){ return [container]; },\r\n                    onLeave: function(){ return [v.activeSection, v.leavingSection, (v.sectionIndex + 1), v.direction]; },\r\n                    afterLoad: function(){ return [v.element, v.anchorLink, (v.sectionIndex + 1)]; },\r\n                    afterSlideLoad: function(){ return [v.destiny, v.anchorLink, (v.sectionIndex + 1), v.slideAnchor, v.slideIndex]; },\r\n                    onSlideLeave: function(){ return [v.prevSlide, v.anchorLink, (v.sectionIndex + 1), v.prevSlideIndex, v.direction, v.slideIndex]; },\r\n                };\r\n            }\r\n\r\n            return paramsPerEvent[eventName]();\r\n        }\r\n\r\n        /**\r\n        * Performs the vertical movement (by CSS3 or by jQuery)\r\n        */\r\n        function performMovement(v){\r\n            // using CSS3 translate functionality\r\n            if (options.css3 && options.autoScrolling && !options.scrollBar) {\r\n\r\n                // The first section can have a negative value in iOS 10. Not quite sure why: -0.0142822265625\r\n                // that's why we round it to 0.\r\n                var translate3d = 'translate3d(0px, -' + Math.round(v.dtop) + 'px, 0px)';\r\n                transformContainer(translate3d, true);\r\n\r\n                //even when the scrollingSpeed is 0 there's a little delay, which might cause the\r\n                //scrollingSpeed to change in case of using silentMoveTo();\r\n                if(options.scrollingSpeed){\r\n                    clearTimeout(afterSectionLoadsId);\r\n                    afterSectionLoadsId = setTimeout(function () {\r\n                        afterSectionLoads(v);\r\n                    }, options.scrollingSpeed);\r\n                }else{\r\n                    afterSectionLoads(v);\r\n                }\r\n            }\r\n\r\n            // using JS to animate\r\n            else{\r\n                var scrollSettings = getScrollSettings(v.dtop);\r\n                FP.test.top = -v.dtop + 'px';\r\n\r\n                scrollTo(scrollSettings.element, scrollSettings.options, options.scrollingSpeed, function(){\r\n                    if(options.scrollBar){\r\n\r\n                        /* Hack!\r\n                        The timeout prevents setting the most dominant section in the viewport as \"active\" when the user\r\n                        scrolled to a smaller section by using the mousewheel (auto scrolling) rather than draging the scroll bar.\r\n\r\n                        When using scrollBar:true It seems like the scroll events still getting propagated even after the scrolling animation has finished.\r\n                        */\r\n                        setTimeout(function(){\r\n                            afterSectionLoads(v);\r\n                        },30);\r\n                    }else{\r\n                        afterSectionLoads(v);\r\n                    }\r\n                });\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Gets the scrolling settings depending on the plugin autoScrolling option\r\n        */\r\n        function getScrollSettings(top){\r\n            var scroll = {};\r\n\r\n            //top property animation\r\n            if(options.autoScrolling && !options.scrollBar){\r\n                scroll.options = -top;\r\n                scroll.element = $(WRAPPER_SEL)[0];\r\n            }\r\n\r\n            //window real scrolling\r\n            else{\r\n                scroll.options = top;\r\n                scroll.element = window;\r\n            }\r\n\r\n            return scroll;\r\n        }\r\n\r\n        /**\r\n        * Adds sections before or after the current one to create the infinite effect.\r\n        */\r\n        function createInfiniteSections(v){\r\n            // Scrolling down\r\n            if (!v.isMovementUp) {\r\n                // Move all previous sections to after the active section\r\n                after($(SECTION_ACTIVE_SEL)[0], prevAll(v.activeSection, SECTION_SEL).reverse());\r\n            }\r\n            else { // Scrolling up\r\n                // Move all next sections to before the active section\r\n                before($(SECTION_ACTIVE_SEL)[0], nextAll(v.activeSection, SECTION_SEL));\r\n            }\r\n\r\n            // Maintain the displayed position (now that we changed the element order)\r\n            silentScroll($(SECTION_ACTIVE_SEL)[0].offsetTop);\r\n\r\n            // Maintain the active slides visible in the viewport\r\n            keepSlidesPosition();\r\n\r\n            // save for later the elements that still need to be reordered\r\n            v.wrapAroundElements = v.activeSection;\r\n\r\n            // Recalculate animation variables\r\n            v.dtop = v.element.offsetTop;\r\n            v.yMovement = getYmovement(v.element);\r\n\r\n            return v;\r\n        }\r\n\r\n        /**\r\n        * Fix section order after continuousVertical changes have been animated\r\n        */\r\n        function continuousVerticalFixSectionOrder (v) {\r\n            // If continuousVertical is in effect (and autoScrolling would also be in effect then),\r\n            // finish moving the elements around so the direct navigation will function more simply\r\n            if (v.wrapAroundElements == null) {\r\n                return;\r\n            }\r\n\r\n            if (v.isMovementUp) {\r\n                before($(SECTION_SEL)[0], v.wrapAroundElements);\r\n            }\r\n            else {\r\n                after($(SECTION_SEL)[$(SECTION_SEL).length-1], v.wrapAroundElements);\r\n            }\r\n\r\n            silentScroll($(SECTION_ACTIVE_SEL)[0].offsetTop);\r\n\r\n            // Maintain the active slides visible in the viewport\r\n            keepSlidesPosition();\r\n        }\r\n\r\n        /**\r\n        * Actions to do once the section is loaded.\r\n        */\r\n        function afterSectionLoads (v){\r\n            continuousVerticalFixSectionOrder(v);\r\n\r\n            //callback (afterLoad) if the site is not just resizing and readjusting the slides\r\n            if(isFunction(options.afterLoad) && !v.localIsResizing){\r\n                fireCallback('afterLoad', v);\r\n            }\r\n\r\n            if(options.scrollOverflow){\r\n                options.scrollOverflowHandler.afterLoad();\r\n            }\r\n\r\n            if(!v.localIsResizing){\r\n                playMedia(v.element);\r\n            }\r\n\r\n            addClass(v.element, COMPLETELY);\r\n            removeClass(siblings(v.element), COMPLETELY);\r\n            lazyLoadOthers();\r\n\r\n            canScroll = true;\r\n\r\n            if(isFunction(v.callback)){\r\n                v.callback();\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Sets the value for the given attribute from the `data-` attribute with the same suffix\r\n        * ie: data-srcset ==> srcset  |  data-src ==> src\r\n        */\r\n        function setSrc(element, attribute){\r\n            element.setAttribute(attribute, element.getAttribute('data-' + attribute));\r\n            element.removeAttribute('data-' + attribute);\r\n        }\r\n\r\n        /**\r\n        * Makes sure lazyload is done for other sections in the viewport that are not the\r\n        * active one. \r\n        */\r\n        function lazyLoadOthers(){\r\n            var hasAutoHeightSections = $(AUTO_HEIGHT_SEL)[0] || isResponsiveMode() && $(AUTO_HEIGHT_RESPONSIVE_SEL)[0];\r\n\r\n            //quitting when it doesn't apply\r\n            if (!options.lazyLoading || !hasAutoHeightSections){\r\n                return;\r\n            }\r\n\r\n            //making sure to lazy load auto-height sections that are in the viewport\r\n            $(SECTION_SEL + ':not(' + ACTIVE_SEL + ')').forEach(function(section){\r\n                if(isSectionInViewport(section)){\r\n                    lazyLoad(section);\r\n                }\r\n            });\r\n        }\r\n\r\n        /**\r\n        * Lazy loads image, video and audio elements.\r\n        */\r\n        function lazyLoad(destiny){\r\n            if (!options.lazyLoading){\r\n                return;\r\n            }\r\n\r\n            var panel = getSlideOrSection(destiny);\r\n\r\n            $('img[data-src], img[data-srcset], source[data-src], source[data-srcset], video[data-src], audio[data-src], iframe[data-src]', panel).forEach(function(element){\r\n                ['src', 'srcset'].forEach(function(type){\r\n                    var attribute = element.getAttribute('data-' + type);\r\n                    if(attribute != null && attribute){\r\n                        setSrc(element, type);\r\n                        element.addEventListener('load', function(){\r\n                            onMediaLoad(destiny);\r\n                        });\r\n                    }\r\n                });\r\n\r\n                if(matches(element, 'source')){\r\n                    var elementToPlay =  closest(element, 'video, audio');\r\n                    if(elementToPlay){\r\n                        elementToPlay.load();\r\n                        elementToPlay.onloadeddata = function(){\r\n                            onMediaLoad(destiny);\r\n                        }\r\n                    }\r\n                }\r\n            });\r\n        }\r\n\r\n        /**\r\n        * Callback firing when a lazy load media element has loaded.\r\n        * Making sure it only fires one per section in normal conditions (if load time is not huge)\r\n        */\r\n        function onMediaLoad(section){\r\n            if(options.scrollOverflow){\r\n                clearTimeout(g_mediaLoadedId);\r\n                g_mediaLoadedId = setTimeout(function(){\r\n                    scrollBarHandler.createScrollBar(section);\r\n                }, 200);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Plays video and audio elements.\r\n        */\r\n        function playMedia(destiny){\r\n            var panel = getSlideOrSection(destiny);\r\n\r\n            //playing HTML5 media elements\r\n            $('video, audio', panel).forEach(function(element){\r\n                if( element.hasAttribute('data-autoplay') && typeof element.play === 'function' ) {\r\n                    element.play();\r\n                }\r\n            });\r\n\r\n            //youtube videos\r\n            $('iframe[src*=\"youtube.com/embed/\"]', panel).forEach(function(element){\r\n                if ( element.hasAttribute('data-autoplay') ){\r\n                    playYoutube(element);\r\n                }\r\n\r\n                //in case the URL was not loaded yet. On page load we need time for the new URL (with the API string) to load.\r\n                element.onload = function() {\r\n                    if ( element.hasAttribute('data-autoplay') ){\r\n                        playYoutube(element);\r\n                    }\r\n                };\r\n            });\r\n        }\r\n\r\n        /**\r\n        * Plays a youtube video\r\n        */\r\n        function playYoutube(element){\r\n            element.contentWindow.postMessage('{\"event\":\"command\",\"func\":\"playVideo\",\"args\":\"\"}', '*');\r\n        }\r\n\r\n        /**\r\n        * Stops video and audio elements.\r\n        */\r\n        function stopMedia(destiny){\r\n            var panel = getSlideOrSection(destiny);\r\n\r\n            //stopping HTML5 media elements\r\n            $('video, audio', panel).forEach(function(element){\r\n                if( !element.hasAttribute('data-keepplaying') && typeof element.pause === 'function' ) {\r\n                    element.pause();\r\n                }\r\n            });\r\n\r\n            //youtube videos\r\n            $('iframe[src*=\"youtube.com/embed/\"]', panel).forEach(function(element){\r\n                if( /youtube\\.com\\/embed\\//.test(element.getAttribute('src')) && !element.hasAttribute('data-keepplaying')){\r\n                    element.contentWindow.postMessage('{\"event\":\"command\",\"func\":\"pauseVideo\",\"args\":\"\"}','*');\r\n                }\r\n            });\r\n        }\r\n\r\n        /**\r\n        * Gets the active slide (or section) for the given section\r\n        */\r\n        function getSlideOrSection(destiny){\r\n            var slide = $(SLIDE_ACTIVE_SEL, destiny);\r\n            if( slide.length ) {\r\n                destiny = slide[0];\r\n            }\r\n\r\n            return destiny;\r\n        }\r\n\r\n        /**\r\n        * Scrolls to the anchor in the URL when loading the site\r\n        */\r\n        function scrollToAnchor(){\r\n            var anchors =  getAnchorsURL();\r\n            var sectionAnchor = anchors.section;\r\n            var slideAnchor = anchors.slide;\r\n\r\n            if(sectionAnchor){  //if theres any #\r\n                if(options.animateAnchor){\r\n                    scrollPageAndSlide(sectionAnchor, slideAnchor);\r\n                }else{\r\n                    silentMoveTo(sectionAnchor, slideAnchor);\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Detecting any change on the URL to scroll to the given anchor link\r\n        * (a way to detect back history button as we play with the hashes on the URL)\r\n        */\r\n        function hashChangeHandler(){\r\n            if(!isScrolling && !options.lockAnchors){\r\n                var anchors = getAnchorsURL();\r\n                var sectionAnchor = anchors.section;\r\n                var slideAnchor = anchors.slide;\r\n\r\n                //when moving to a slide in the first section for the first time (first time to add an anchor to the URL)\r\n                var isFirstSlideMove =  (typeof lastScrolledDestiny === 'undefined');\r\n                var isFirstScrollMove = (typeof lastScrolledDestiny === 'undefined' && typeof slideAnchor === 'undefined' && !slideMoving);\r\n\r\n                if(sectionAnchor && sectionAnchor.length){\r\n                    /*in order to call scrollpage() only once for each destination at a time\r\n                    It is called twice for each scroll otherwise, as in case of using anchorlinks `hashChange`\r\n                    event is fired on every scroll too.*/\r\n                    if ((sectionAnchor && sectionAnchor !== lastScrolledDestiny) && !isFirstSlideMove\r\n                        || isFirstScrollMove\r\n                        || (!slideMoving && lastScrolledSlide != slideAnchor )){\r\n\r\n                        scrollPageAndSlide(sectionAnchor, slideAnchor);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        //gets the URL anchors (section and slide)\r\n        function getAnchorsURL(){\r\n            var section;\r\n            var slide;\r\n            var hash = window.location.hash;\r\n\r\n            if(hash.length){\r\n                //getting the anchor link in the URL and deleting the `#`\r\n                var anchorsParts =  hash.replace('#', '').split('/');\r\n\r\n                //using / for visual reasons and not as a section/slide separator #2803\r\n                var isFunkyAnchor = hash.indexOf('#/') > -1;\r\n\r\n                section = isFunkyAnchor ? '/' + anchorsParts[1] : decodeURIComponent(anchorsParts[0]);\r\n\r\n                var slideAnchor = isFunkyAnchor ? anchorsParts[2] : anchorsParts[1];\r\n                if(slideAnchor && slideAnchor.length){\r\n                    slide = decodeURIComponent(slideAnchor);\r\n                }\r\n            }\r\n\r\n            return {\r\n                section: section,\r\n                slide: slide\r\n            };\r\n        }\r\n\r\n        //Sliding with arrow keys, both, vertical and horizontal\r\n        function keydownHandler(e) {\r\n            clearTimeout(keydownId);\r\n\r\n            var activeElement = document.activeElement;\r\n            var keyCode = e.keyCode;\r\n\r\n            //tab?\r\n            if(keyCode === 9){\r\n                onTab(e);\r\n            }\r\n\r\n            else if(!matches(activeElement, 'textarea') && !matches(activeElement, 'input') && !matches(activeElement, 'select') &&\r\n                activeElement.getAttribute('contentEditable') !== \"true\" && activeElement.getAttribute('contentEditable') !== '' &&\r\n                options.keyboardScrolling && options.autoScrolling){\r\n\r\n                //preventing the scroll with arrow keys & spacebar & Page Up & Down keys\r\n                var keyControls = [40, 38, 32, 33, 34];\r\n                if(keyControls.indexOf(keyCode) > -1){\r\n                    preventDefault(e);\r\n                }\r\n\r\n                controlPressed = e.ctrlKey;\r\n\r\n                keydownId = setTimeout(function(){\r\n                    onkeydown(e);\r\n                },150);\r\n            }\r\n        }\r\n\r\n        function tooltipTextHandler(){\r\n            /*jshint validthis:true */\r\n            trigger(prev(this), 'click');\r\n        }\r\n\r\n        //to prevent scrolling while zooming\r\n        function keyUpHandler(e){\r\n            if(isWindowFocused){ //the keyup gets fired on new tab ctrl + t in Firefox\r\n                controlPressed = e.ctrlKey;\r\n            }\r\n        }\r\n\r\n        //binding the mousemove when the mouse's middle button is released\r\n        function mouseDownHandler(e){\r\n            //middle button\r\n            if (e.which == 2){\r\n                oldPageY = e.pageY;\r\n                container.addEventListener('mousemove', mouseMoveHandler);\r\n            }\r\n        }\r\n\r\n        //unbinding the mousemove when the mouse's middle button is released\r\n        function mouseUpHandler(e){\r\n            //middle button\r\n            if (e.which == 2){\r\n                container.removeEventListener('mousemove', mouseMoveHandler);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Makes sure the tab key will only focus elements within the current section/slide\r\n        * preventing this way from breaking the page.\r\n        * Based on \"Modals and keyboard traps\"\r\n        * from https://developers.google.com/web/fundamentals/accessibility/focus/using-tabindex\r\n        */\r\n        function onTab(e){\r\n            var isShiftPressed = e.shiftKey;\r\n            var activeElement = document.activeElement;\r\n            var focusableElements = getFocusables(getSlideOrSection($(SECTION_ACTIVE_SEL)[0]));\r\n\r\n            function preventAndFocusFirst(e){\r\n                preventDefault(e);\r\n                return focusableElements[0] ? focusableElements[0].focus() : null;\r\n            }\r\n\r\n            //outside any section or slide? Let's not hijack the tab!\r\n            if(isFocusOutside(e)){\r\n                return;\r\n            }\r\n\r\n            //is there an element with focus?\r\n            if(activeElement){\r\n                if(closest(activeElement, SECTION_ACTIVE_SEL + ',' + SECTION_ACTIVE_SEL + ' ' + SLIDE_ACTIVE_SEL) == null){\r\n                    activeElement = preventAndFocusFirst(e);\r\n                }\r\n            }\r\n\r\n            //no element if focused? Let's focus the first one of the section/slide\r\n            else{\r\n                preventAndFocusFirst(e);\r\n            }\r\n\r\n            //when reached the first or last focusable element of the section/slide\r\n            //we prevent the tab action to keep it in the last focusable element\r\n            if(!isShiftPressed && activeElement == focusableElements[focusableElements.length - 1] ||\r\n                isShiftPressed && activeElement == focusableElements[0]\r\n            ){\r\n                preventDefault(e);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Gets all the focusable elements inside the passed element.\r\n        */\r\n        function getFocusables(el){\r\n            return [].slice.call($(focusableElementsString, el)).filter(function(item) {\r\n                    return item.getAttribute('tabindex') !== '-1'\r\n                    //are also not hidden elements (or with hidden parents)\r\n                    && item.offsetParent !== null;\r\n            });\r\n        }\r\n\r\n        /**\r\n        * Determines whether the focus is outside fullpage.js sections/slides or not.\r\n        */\r\n        function isFocusOutside(e){\r\n            var allFocusables = getFocusables(document);\r\n            var currentFocusIndex = allFocusables.indexOf(document.activeElement);\r\n            var focusDestinationIndex = e.shiftKey ? currentFocusIndex - 1 : currentFocusIndex + 1;\r\n            var focusDestination = allFocusables[focusDestinationIndex];\r\n            var destinationItemSlide = nullOrSlide(closest(focusDestination, SLIDE_SEL));\r\n            var destinationItemSection = nullOrSection(closest(focusDestination, SECTION_SEL));\r\n\r\n            return !destinationItemSlide && !destinationItemSection;\r\n        }\r\n\r\n        //Scrolling horizontally when clicking on the slider controls.\r\n        function slideArrowHandler(){\r\n            /*jshint validthis:true */\r\n            var section = closest(this, SECTION_SEL);\r\n\r\n            /*jshint validthis:true */\r\n            if (hasClass(this, SLIDES_PREV)) {\r\n                if(isScrollAllowed.m.left){\r\n                    moveSlideLeft(section);\r\n                }\r\n            } else {\r\n                if(isScrollAllowed.m.right){\r\n                    moveSlideRight(section);\r\n                }\r\n            }\r\n        }\r\n\r\n        //when opening a new tab (ctrl + t), `control` won't be pressed when coming back.\r\n        function blurHandler(){\r\n            isWindowFocused = false;\r\n            controlPressed = false;\r\n        }\r\n\r\n        //Scrolls to the section when clicking the navigation bullet\r\n        function sectionBulletHandler(e){\r\n            preventDefault(e);\r\n\r\n            /*jshint validthis:true */\r\n            var indexBullet = index(closest(this, SECTION_NAV_SEL + ' li'));\r\n            scrollPage($(SECTION_SEL)[indexBullet]);\r\n        }\r\n\r\n        //Scrolls the slider to the given slide destination for the given section\r\n        function slideBulletHandler(e){\r\n            preventDefault(e);\r\n\r\n            /*jshint validthis:true */\r\n            var slides = $(SLIDES_WRAPPER_SEL, closest(this, SECTION_SEL))[0];\r\n            var destiny = $(SLIDE_SEL, slides)[index(closest(this, 'li'))];\r\n\r\n            landscapeScroll(slides, destiny);\r\n        }\r\n\r\n        //Menu item handler when not using anchors or using lockAnchors:true\r\n        function menuItemsHandler(e){\r\n            if($(options.menu)[0] && (options.lockAnchors || !options.anchors.length)){\r\n                preventDefault(e);\r\n                moveTo(this.getAttribute('data-menuanchor'));\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Keydown event\r\n        */\r\n        function onkeydown(e){\r\n            var shiftPressed = e.shiftKey;\r\n            var activeElement = document.activeElement;\r\n            var isMediaFocused = matches(activeElement, 'video') || matches(activeElement, 'audio');\r\n\r\n            //do nothing if we can not scroll or we are not using horizotnal key arrows.\r\n            if(!canScroll && [37,39].indexOf(e.keyCode) < 0){\r\n                return;\r\n            }\r\n\r\n            switch (e.keyCode) {\r\n                //up\r\n                case 38:\r\n                case 33:\r\n                    if(isScrollAllowed.k.up){\r\n                        moveSectionUp();\r\n                    }\r\n                    break;\r\n\r\n                //down\r\n                case 32: //spacebar\r\n\r\n                    if(shiftPressed && isScrollAllowed.k.up && !isMediaFocused){\r\n                        moveSectionUp();\r\n                        break;\r\n                    }\r\n                /* falls through */\r\n                case 40:\r\n                case 34:\r\n                    if(isScrollAllowed.k.down){\r\n                        // space bar?\r\n                        if(e.keyCode !== 32 || !isMediaFocused){\r\n                            moveSectionDown();\r\n                        }\r\n                    }\r\n                    break;\r\n\r\n                //Home\r\n                case 36:\r\n                    if(isScrollAllowed.k.up){\r\n                        moveTo(1);\r\n                    }\r\n                    break;\r\n\r\n                //End\r\n                case 35:\r\n                     if(isScrollAllowed.k.down){\r\n                        moveTo( $(SECTION_SEL).length );\r\n                    }\r\n                    break;\r\n\r\n                //left\r\n                case 37:\r\n                    if(isScrollAllowed.k.left){\r\n                        moveSlideLeft();\r\n                    }\r\n                    break;\r\n\r\n                //right\r\n                case 39:\r\n                    if(isScrollAllowed.k.right){\r\n                        moveSlideRight();\r\n                    }\r\n                    break;\r\n\r\n                default:\r\n                    return; // exit this handler for other keys\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Detecting the direction of the mouse movement.\r\n        * Used only for the middle button of the mouse.\r\n        */\r\n        var oldPageY = 0;\r\n        function mouseMoveHandler(e){\r\n            if(!options.autoScrolling){\r\n                return;\r\n            }\r\n            if(canScroll){\r\n                // moving up\r\n                if (e.pageY < oldPageY && isScrollAllowed.m.up){\r\n                    moveSectionUp();\r\n                }\r\n\r\n                // moving down\r\n                else if(e.pageY > oldPageY && isScrollAllowed.m.down){\r\n                    moveSectionDown();\r\n                }\r\n            }\r\n            oldPageY = e.pageY;\r\n        }\r\n\r\n        /**\r\n        * Scrolls horizontal sliders.\r\n        */\r\n        function landscapeScroll(slides, destiny, direction){\r\n            var section = closest(slides, SECTION_SEL);\r\n            var v = {\r\n                slides: slides,\r\n                destiny: destiny,\r\n                direction: direction,\r\n                destinyPos: {left: destiny.offsetLeft},\r\n                slideIndex: index(destiny),\r\n                section: section,\r\n                sectionIndex: index(section, SECTION_SEL),\r\n                anchorLink: section.getAttribute('data-anchor'),\r\n                slidesNav: $(SLIDES_NAV_SEL, section)[0],\r\n                slideAnchor: getAnchor(destiny),\r\n                prevSlide: $(SLIDE_ACTIVE_SEL, section)[0],\r\n                prevSlideIndex: index($(SLIDE_ACTIVE_SEL, section)[0]),\r\n\r\n                //caching the value of isResizing at the momment the function is called\r\n                //because it will be checked later inside a setTimeout and the value might change\r\n                localIsResizing: isResizing\r\n            };\r\n            v.xMovement = getXmovement(v.prevSlideIndex, v.slideIndex);\r\n            v.direction = v.direction ? v.direction : v.xMovement;\r\n\r\n            //important!! Only do it when not resizing\r\n            if(!v.localIsResizing){\r\n                //preventing from scrolling to the next/prev section when using scrollHorizontally\r\n                canScroll = false;\r\n            }\r\n\r\n            if(options.onSlideLeave){\r\n\r\n                //if the site is not just resizing and readjusting the slides\r\n                if(!v.localIsResizing && v.xMovement!=='none'){\r\n                    if(isFunction( options.onSlideLeave )){\r\n                        if( fireCallback('onSlideLeave', v) === false){\r\n                            slideMoving = false;\r\n                            return;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            addClass(destiny, ACTIVE);\r\n            removeClass(siblings(destiny), ACTIVE);\r\n\r\n            if(!v.localIsResizing){\r\n                stopMedia(v.prevSlide);\r\n                lazyLoad(destiny);\r\n            }\r\n\r\n            if(!options.loopHorizontal && options.controlArrows){\r\n                //hidding it for the fist slide, showing for the rest\r\n                toggle($(SLIDES_ARROW_PREV_SEL, section), v.slideIndex!==0);\r\n\r\n                //hidding it for the last slide, showing for the rest\r\n                toggle($(SLIDES_ARROW_NEXT_SEL, section), next(destiny) != null);\r\n            }\r\n\r\n            //only changing the URL if the slides are in the current section (not for resize re-adjusting)\r\n            if(hasClass(section, ACTIVE) && !v.localIsResizing){\r\n                setState(v.slideIndex, v.slideAnchor, v.anchorLink, v.sectionIndex);\r\n            }\r\n\r\n            performHorizontalMove(slides, v, true);\r\n        }\r\n\r\n\r\n        function afterSlideLoads(v){\r\n            activeSlidesNavigation(v.slidesNav, v.slideIndex);\r\n\r\n            //if the site is not just resizing and readjusting the slides\r\n            if(!v.localIsResizing){\r\n                if(isFunction( options.afterSlideLoad )){\r\n                    fireCallback('afterSlideLoad', v);\r\n                }\r\n\r\n                //needs to be inside the condition to prevent problems with continuousVertical and scrollHorizontally\r\n                //and to prevent double scroll right after a windows resize\r\n                canScroll = true;\r\n\r\n                playMedia(v.destiny);\r\n            }\r\n\r\n            //letting them slide again\r\n            slideMoving = false;\r\n        }\r\n\r\n        /**\r\n        * Performs the horizontal movement. (CSS3 or jQuery)\r\n        *\r\n        * @param fireCallback {Bool} - determines whether or not to fire the callback\r\n        */\r\n        function performHorizontalMove(slides, v, fireCallback){\r\n            var destinyPos = v.destinyPos;\r\n\r\n            if(options.css3){\r\n                var translate3d = 'translate3d(-' + Math.round(destinyPos.left) + 'px, 0px, 0px)';\r\n\r\n                FP.test.translate3dH[v.sectionIndex] = translate3d;\r\n                css(addAnimation($(SLIDES_CONTAINER_SEL, slides)), getTransforms(translate3d));\r\n\r\n                afterSlideLoadsId = setTimeout(function(){\r\n                    if(fireCallback){\r\n                        afterSlideLoads(v);\r\n                    }\r\n                }, options.scrollingSpeed);\r\n            }else{\r\n                FP.test.left[v.sectionIndex] = Math.round(destinyPos.left);\r\n\r\n                scrollTo(slides, Math.round(destinyPos.left), options.scrollingSpeed, function(){\r\n                    if(fireCallback){\r\n                        afterSlideLoads(v);\r\n                    }\r\n                });\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Sets the state for the horizontal bullet navigations.\r\n        */\r\n        function activeSlidesNavigation(slidesNav, slideIndex){\r\n            if(options.slidesNavigation && slidesNav != null){\r\n                removeClass($(ACTIVE_SEL, slidesNav), ACTIVE);\r\n                addClass( $('a', $('li', slidesNav)[slideIndex] ), ACTIVE);\r\n            }\r\n        }\r\n\r\n        var previousHeight = windowsHeight;\r\n\r\n        /*\r\n        * Resize event handler.\r\n        */        \r\n        function resizeHandler(){\r\n            clearTimeout(resizeId);\r\n\r\n            //in order to call the functions only when the resize is finished\r\n            //http://stackoverflow.com/questions/4298612/jquery-how-to-call-resize-event-only-once-its-finished-resizing    \r\n            resizeId = setTimeout(function(){\r\n\r\n                //issue #3336 \r\n                //(some apps or browsers, like Chrome/Firefox for Mobile take time to report the real height)\r\n                //so we check it 3 times with intervals in that case\r\n                for(var i = 0; i< 4; i++){\r\n                    resizeHandlerId = setTimeout(resizeActions, 200 * i);\r\n                }\r\n            }, 200);\r\n        }\r\n\r\n        /**\r\n        * When resizing the site, we adjust the heights of the sections, slimScroll...\r\n        */\r\n        function resizeActions(){\r\n\r\n            //checking if it needs to get responsive\r\n            responsive();\r\n\r\n            // rebuild immediately on touch devices\r\n            if (isTouchDevice) {\r\n                var activeElement = document.activeElement;\r\n\r\n                //if the keyboard is NOT visible\r\n                if (!matches(activeElement, 'textarea') && !matches(activeElement, 'input') && !matches(activeElement, 'select')) {\r\n                    var currentHeight = getWindowHeight();\r\n\r\n                    //making sure the change in the viewport size is enough to force a rebuild. (20 % of the window to avoid problems when hidding scroll bars)\r\n                    if( Math.abs(currentHeight - previousHeight) > (20 * Math.max(previousHeight, currentHeight) / 100) ){\r\n                        reBuild(true);\r\n                        previousHeight = currentHeight;\r\n                    }\r\n                }\r\n            }\r\n            else{\r\n                adjustToNewViewport();\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Checks if the site needs to get responsive and disables autoScrolling if so.\r\n        * A class `fp-responsive` is added to the plugin's container in case the user wants to use it for his own responsive CSS.\r\n        */\r\n        function responsive(){\r\n            var widthLimit = options.responsive || options.responsiveWidth; //backwards compatiblity\r\n            var heightLimit = options.responsiveHeight;\r\n\r\n            //only calculating what we need. Remember its called on the resize event.\r\n            var isBreakingPointWidth = widthLimit && window.innerWidth < widthLimit;\r\n            var isBreakingPointHeight = heightLimit && window.innerHeight < heightLimit;\r\n\r\n            if(widthLimit && heightLimit){\r\n                setResponsive(isBreakingPointWidth || isBreakingPointHeight);\r\n            }\r\n            else if(widthLimit){\r\n                setResponsive(isBreakingPointWidth);\r\n            }\r\n            else if(heightLimit){\r\n                setResponsive(isBreakingPointHeight);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Adds transition animations for the given element\r\n        */\r\n        function addAnimation(element){\r\n            var transition = 'all ' + options.scrollingSpeed + 'ms ' + options.easingcss3;\r\n\r\n            removeClass(element, NO_TRANSITION);\r\n            return css(element, {\r\n                '-webkit-transition': transition,\r\n                'transition': transition\r\n            });\r\n        }\r\n\r\n        /**\r\n        * Remove transition animations for the given element\r\n        */\r\n        function removeAnimation(element){\r\n            return addClass(element, NO_TRANSITION);\r\n        }\r\n\r\n        /**\r\n        * Activating the vertical navigation bullets according to the given slide name.\r\n        */\r\n        function activateNavDots(name, sectionIndex){\r\n            if(options.navigation && $(SECTION_NAV_SEL)[0] != null){\r\n                    removeClass($(ACTIVE_SEL, $(SECTION_NAV_SEL)[0]), ACTIVE);\r\n                if(name){\r\n                    addClass( $('a[href=\"#' + name + '\"]', $(SECTION_NAV_SEL)[0]), ACTIVE);\r\n                }else{\r\n                    addClass($('a', $('li', $(SECTION_NAV_SEL)[0])[sectionIndex]), ACTIVE);\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Activating the website main menu elements according to the given slide name.\r\n        */\r\n        function activateMenuElement(name){\r\n            $(options.menu).forEach(function(menu) {\r\n                if(options.menu && menu != null){\r\n                    removeClass($(ACTIVE_SEL, menu), ACTIVE);\r\n                    addClass($('[data-menuanchor=\"'+name+'\"]', menu), ACTIVE);\r\n                }\r\n            });\r\n        }\r\n\r\n        /**\r\n        * Sets to active the current menu and vertical nav items.\r\n        */\r\n        function activateMenuAndNav(anchor, index){\r\n            activateMenuElement(anchor);\r\n            activateNavDots(anchor, index);\r\n        }\r\n\r\n        /**\r\n        * Retuns `up` or `down` depending on the scrolling movement to reach its destination\r\n        * from the current section.\r\n        */\r\n        function getYmovement(destiny){\r\n            var fromIndex = index($(SECTION_ACTIVE_SEL)[0], SECTION_SEL);\r\n            var toIndex = index(destiny, SECTION_SEL);\r\n            if( fromIndex == toIndex){\r\n                return 'none';\r\n            }\r\n            if(fromIndex > toIndex){\r\n                return 'up';\r\n            }\r\n            return 'down';\r\n        }\r\n\r\n        /**\r\n        * Retuns `right` or `left` depending on the scrolling movement to reach its destination\r\n        * from the current slide.\r\n        */\r\n        function getXmovement(fromIndex, toIndex){\r\n            if( fromIndex == toIndex){\r\n                return 'none';\r\n            }\r\n            if(fromIndex > toIndex){\r\n                return 'left';\r\n            }\r\n            return 'right';\r\n        }\r\n\r\n        function addTableClass(element){\r\n            //In case we are styling for the 2nd time as in with reponsiveSlides\r\n            if(!hasClass(element, TABLE)){\r\n                var wrapper = document.createElement('div');\r\n                wrapper.className = TABLE_CELL;\r\n                wrapper.style.height = getTableHeight(element) + 'px';\r\n\r\n                addClass(element, TABLE);\r\n                wrapInner(element, wrapper);\r\n            }\r\n        }\r\n\r\n        function getTableHeight(element){\r\n            var sectionHeight = windowsHeight;\r\n\r\n            if(options.paddingTop || options.paddingBottom){\r\n                var section = element;\r\n                if(!hasClass(section, SECTION)){\r\n                    section = closest(element, SECTION_SEL);\r\n                }\r\n\r\n                var paddings = parseInt(getComputedStyle(section)['padding-top']) + parseInt(getComputedStyle(section)['padding-bottom']);\r\n                sectionHeight = (windowsHeight - paddings);\r\n            }\r\n\r\n            return sectionHeight;\r\n        }\r\n\r\n        /**\r\n        * Adds a css3 transform property to the container class with or without animation depending on the animated param.\r\n        */\r\n        function transformContainer(translate3d, animated){\r\n            if(animated){\r\n                addAnimation(container);\r\n            }else{\r\n                removeAnimation(container);\r\n            }\r\n\r\n            css(container, getTransforms(translate3d));\r\n            FP.test.translate3d = translate3d;\r\n\r\n            //syncronously removing the class after the animation has been applied.\r\n            setTimeout(function(){\r\n                removeClass(container, NO_TRANSITION);\r\n            },10);\r\n        }\r\n\r\n        /**\r\n        * Gets a section by its anchor / index\r\n        */\r\n        function getSectionByAnchor(sectionAnchor){\r\n            var section = $(SECTION_SEL + '[data-anchor=\"'+sectionAnchor+'\"]', container)[0];\r\n            if(!section){\r\n                var sectionIndex = typeof sectionAnchor !== 'undefined' ? sectionAnchor -1 : 0;\r\n                section = $(SECTION_SEL)[sectionIndex];\r\n            }\r\n\r\n            return section;\r\n        }\r\n\r\n        /**\r\n        * Gets a slide inside a given section by its anchor / index\r\n        */\r\n        function getSlideByAnchor(slideAnchor, section){\r\n            var slide = $(SLIDE_SEL + '[data-anchor=\"'+slideAnchor+'\"]', section)[0];\r\n            if(slide == null){\r\n                slideAnchor = typeof slideAnchor !== 'undefined' ? slideAnchor : 0;\r\n                slide = $(SLIDE_SEL, section)[slideAnchor];\r\n            }\r\n\r\n            return slide;\r\n        }\r\n\r\n        /**\r\n        * Scrolls to the given section and slide anchors\r\n        */\r\n        function scrollPageAndSlide(sectionAnchor, slideAnchor){\r\n            var section = getSectionByAnchor(sectionAnchor);\r\n\r\n            //do nothing if there's no section with the given anchor name\r\n            if(section == null) return;\r\n\r\n            var slide = getSlideByAnchor(slideAnchor, section);\r\n\r\n            //we need to scroll to the section and then to the slide\r\n            if (getAnchor(section) !== lastScrolledDestiny && !hasClass(section, ACTIVE)){\r\n                scrollPage(section, function(){\r\n                    scrollSlider(slide);\r\n                });\r\n            }\r\n            //if we were already in the section\r\n            else{\r\n                scrollSlider(slide);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Scrolls the slider to the given slide destination for the given section\r\n        */\r\n        function scrollSlider(slide){\r\n            if(slide != null){\r\n                landscapeScroll(closest(slide, SLIDES_WRAPPER_SEL), slide);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Creates a landscape navigation bar with dots for horizontal sliders.\r\n        */\r\n        function addSlidesNavigation(section, numSlides){\r\n            appendTo(createElementFromHTML('<div class=\"' + SLIDES_NAV + '\"><ul></ul></div>'), section);\r\n            var nav = $(SLIDES_NAV_SEL, section)[0];\r\n\r\n            //top or bottom\r\n            addClass(nav, 'fp-' + options.slidesNavPosition);\r\n\r\n            for(var i=0; i< numSlides; i++){\r\n                appendTo(createElementFromHTML('<li><a href=\"#\"><span class=\"fp-sr-only\">'+ getBulletLinkName(i, 'Slide') +'</span><span></span></a></li>'), $('ul', nav)[0] );\r\n            }\r\n\r\n            //centering it\r\n            css(nav, {'margin-left': '-' + (nav.innerWidth/2) + 'px'});\r\n\r\n            addClass($('a', $('li', nav)[0] ), ACTIVE);\r\n        }\r\n\r\n\r\n        /**\r\n        * Sets the state of the website depending on the active section/slide.\r\n        * It changes the URL hash when needed and updates the body class.\r\n        */\r\n        function setState(slideIndex, slideAnchor, anchorLink, sectionIndex){\r\n            var sectionHash = '';\r\n\r\n            if(options.anchors.length && !options.lockAnchors){\r\n\r\n                //isn't it the first slide?\r\n                if(slideIndex){\r\n                    if(anchorLink != null){\r\n                        sectionHash = anchorLink;\r\n                    }\r\n\r\n                    //slide without anchor link? We take the index instead.\r\n                    if(slideAnchor == null){\r\n                        slideAnchor = slideIndex;\r\n                    }\r\n\r\n                    lastScrolledSlide = slideAnchor;\r\n                    setUrlHash(sectionHash + '/' + slideAnchor);\r\n\r\n                //first slide won't have slide anchor, just the section one\r\n                }else if(slideIndex != null){\r\n                    lastScrolledSlide = slideAnchor;\r\n                    setUrlHash(anchorLink);\r\n                }\r\n\r\n                //section without slides\r\n                else{\r\n                    setUrlHash(anchorLink);\r\n                }\r\n            }\r\n\r\n            setBodyClass();\r\n        }\r\n\r\n        /**\r\n        * Sets the URL hash.\r\n        */\r\n        function setUrlHash(url){\r\n            if(options.recordHistory){\r\n                location.hash = url;\r\n            }else{\r\n                //Mobile Chrome doesn't work the normal way, so... lets use HTML5 for phones :)\r\n                if(isTouchDevice || isTouch){\r\n                    window.history.replaceState(undefined, undefined, '#' + url);\r\n                }else{\r\n                    var baseUrl = window.location.href.split('#')[0];\r\n                    window.location.replace( baseUrl + '#' + url );\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Gets the anchor for the given slide / section. Its index will be used if there's none.\r\n        */\r\n        function getAnchor(element){\r\n            if(!element){\r\n                return null;\r\n            }\r\n            var anchor = element.getAttribute('data-anchor');\r\n            var elementIndex = index(element);\r\n\r\n            //Slide without anchor link? We take the index instead.\r\n            if(anchor == null){\r\n                anchor = elementIndex;\r\n            }\r\n\r\n            return anchor;\r\n        }\r\n\r\n        /**\r\n        * Sets a class for the body of the page depending on the active section / slide\r\n        */\r\n        function setBodyClass(){\r\n            var section = $(SECTION_ACTIVE_SEL)[0];\r\n            var slide = $(SLIDE_ACTIVE_SEL, section)[0];\r\n\r\n            var sectionAnchor = getAnchor(section);\r\n            var slideAnchor = getAnchor(slide);\r\n\r\n            var text = String(sectionAnchor);\r\n\r\n            if(slide){\r\n                text = text + '-' + slideAnchor;\r\n            }\r\n\r\n            //changing slash for dash to make it a valid CSS style\r\n            text = text.replace('/', '-').replace('#','');\r\n\r\n            //removing previous anchor classes\r\n            var classRe = new RegExp('\\\\b\\\\s?' + VIEWING_PREFIX + '-[^\\\\s]+\\\\b', \"g\");\r\n            $body.className = $body.className.replace(classRe, '');\r\n\r\n            //adding the current anchor\r\n            addClass($body, VIEWING_PREFIX + '-' + text);\r\n        }\r\n\r\n        /**\r\n        * Checks for translate3d support\r\n        * @return boolean\r\n        * http://stackoverflow.com/questions/5661671/detecting-transform-translate3d-support\r\n        */\r\n        function support3d() {\r\n            var el = document.createElement('p'),\r\n                has3d,\r\n                transforms = {\r\n                    'webkitTransform':'-webkit-transform',\r\n                    'OTransform':'-o-transform',\r\n                    'msTransform':'-ms-transform',\r\n                    'MozTransform':'-moz-transform',\r\n                    'transform':'transform'\r\n                };\r\n\r\n            //preventing the style p:empty{display: none;} from returning the wrong result\r\n            el.style.display = 'block'\r\n\r\n            // Add it to the body to get the computed style.\r\n            document.body.insertBefore(el, null);\r\n\r\n            for (var t in transforms) {\r\n                if (el.style[t] !== undefined) {\r\n                    el.style[t] = 'translate3d(1px,1px,1px)';\r\n                    has3d = window.getComputedStyle(el).getPropertyValue(transforms[t]);\r\n                }\r\n            }\r\n\r\n            document.body.removeChild(el);\r\n\r\n            return (has3d !== undefined && has3d.length > 0 && has3d !== 'none');\r\n        }\r\n\r\n        /**\r\n        * Removes the auto scrolling action fired by the mouse wheel and trackpad.\r\n        * After this function is called, the mousewheel and trackpad movements won't scroll through sections.\r\n        */\r\n        function removeMouseWheelHandler(){\r\n            if (document.addEventListener) {\r\n                document.removeEventListener('mousewheel', MouseWheelHandler, false); //IE9, Chrome, Safari, Oper\r\n                document.removeEventListener('wheel', MouseWheelHandler, false); //Firefox\r\n                document.removeEventListener('MozMousePixelScroll', MouseWheelHandler, false); //old Firefox\r\n            } else {\r\n                document.detachEvent('onmousewheel', MouseWheelHandler); //IE 6/7/8\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Adds the auto scrolling action for the mouse wheel and trackpad.\r\n        * After this function is called, the mousewheel and trackpad movements will scroll through sections\r\n        * https://developer.mozilla.org/en-US/docs/Web/Events/wheel\r\n        */\r\n        function addMouseWheelHandler(){\r\n            var prefix = '';\r\n            var _addEventListener;\r\n\r\n            if (window.addEventListener){\r\n                _addEventListener = \"addEventListener\";\r\n            }else{\r\n                _addEventListener = \"attachEvent\";\r\n                prefix = 'on';\r\n            }\r\n\r\n            // detect available wheel event\r\n            var support = 'onwheel' in document.createElement('div') ? 'wheel' : // Modern browsers support \"wheel\"\r\n                      document.onmousewheel !== undefined ? 'mousewheel' : // Webkit and IE support at least \"mousewheel\"\r\n                      'DOMMouseScroll'; // let's assume that remaining browsers are older Firefox\r\n            var passiveEvent = g_supportsPassive ? {passive: false }: false;\r\n\r\n            if(support == 'DOMMouseScroll'){\r\n                document[ _addEventListener ](prefix + 'MozMousePixelScroll', MouseWheelHandler, passiveEvent);\r\n            }\r\n\r\n            //handle MozMousePixelScroll in older Firefox\r\n            else{\r\n                document[ _addEventListener ](prefix + support, MouseWheelHandler, passiveEvent);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Binding the mousemove when the mouse's middle button is pressed\r\n        */\r\n        function addMiddleWheelHandler(){\r\n            container.addEventListener('mousedown', mouseDownHandler);\r\n            container.addEventListener('mouseup', mouseUpHandler);\r\n        }\r\n\r\n        /**\r\n        * Unbinding the mousemove when the mouse's middle button is released\r\n        */\r\n        function removeMiddleWheelHandler(){\r\n            container.removeEventListener('mousedown', mouseDownHandler);\r\n            container.removeEventListener('mouseup', mouseUpHandler);\r\n        }\r\n\r\n        /**\r\n        * Adds the possibility to auto scroll through sections on touch devices.\r\n        */\r\n        function addTouchHandler(){\r\n            if(isTouchDevice || isTouch){\r\n                if(options.autoScrolling){\r\n                    $body.removeEventListener(events.touchmove, preventBouncing, {passive: false});\r\n                    $body.addEventListener(events.touchmove, preventBouncing, {passive: false});\r\n                }\r\n\r\n                var touchWrapper = options.touchWrapper;\r\n                touchWrapper.removeEventListener(events.touchstart, touchStartHandler);\r\n                touchWrapper.removeEventListener(events.touchmove, touchMoveHandler, {passive: false});\r\n\r\n                touchWrapper.addEventListener(events.touchstart, touchStartHandler);\r\n                touchWrapper.addEventListener(events.touchmove, touchMoveHandler, {passive: false});\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Removes the auto scrolling for touch devices.\r\n        */\r\n        function removeTouchHandler(){\r\n            if(isTouchDevice || isTouch){\r\n                // normalScrollElements requires it off #2691\r\n                if(options.autoScrolling){\r\n                    $body.removeEventListener(events.touchmove, touchMoveHandler, {passive: false});\r\n                    $body.removeEventListener(events.touchmove, preventBouncing, {passive: false});\r\n                }\r\n\r\n                var touchWrapper = options.touchWrapper;\r\n                touchWrapper.removeEventListener(events.touchstart, touchStartHandler);\r\n                touchWrapper.removeEventListener(events.touchmove, touchMoveHandler, {passive: false});\r\n            }\r\n        }\r\n\r\n        /*\r\n        * Returns and object with Microsoft pointers (for IE<11 and for IE >= 11)\r\n        * http://msdn.microsoft.com/en-us/library/ie/dn304886(v=vs.85).aspx\r\n        */\r\n        function getMSPointer(){\r\n            var pointer;\r\n\r\n            //IE >= 11 & rest of browsers\r\n            if(window.PointerEvent){\r\n                pointer = { down: 'pointerdown', move: 'pointermove'};\r\n            }\r\n\r\n            //IE < 11\r\n            else{\r\n                pointer = { down: 'MSPointerDown', move: 'MSPointerMove'};\r\n            }\r\n\r\n            return pointer;\r\n        }\r\n\r\n        /**\r\n        * Gets the pageX and pageY properties depending on the browser.\r\n        * https://github.com/alvarotrigo/fullPage.js/issues/194#issuecomment-34069854\r\n        */\r\n        function getEventsPage(e){\r\n            var events = [];\r\n\r\n            events.y = (typeof e.pageY !== 'undefined' && (e.pageY || e.pageX) ? e.pageY : e.touches[0].pageY);\r\n            events.x = (typeof e.pageX !== 'undefined' && (e.pageY || e.pageX) ? e.pageX : e.touches[0].pageX);\r\n\r\n            //in touch devices with scrollBar:true, e.pageY is detected, but we have to deal with touch events. #1008\r\n            if(isTouch && isReallyTouch(e) && options.scrollBar && typeof e.touches !== 'undefined'){\r\n                events.y = e.touches[0].pageY;\r\n                events.x = e.touches[0].pageX;\r\n            }\r\n\r\n            return events;\r\n        }\r\n\r\n        /**\r\n        * Slides silently (with no animation) the active slider to the given slide.\r\n        * @param noCallback {bool} true or defined -> no callbacks\r\n        */\r\n        function silentLandscapeScroll(activeSlide, noCallbacks){\r\n            setScrollingSpeed(0, 'internal');\r\n\r\n            if(typeof noCallbacks !== 'undefined'){\r\n                //preventing firing callbacks afterSlideLoad etc.\r\n                isResizing = true;\r\n            }\r\n\r\n            landscapeScroll(closest(activeSlide, SLIDES_WRAPPER_SEL), activeSlide);\r\n\r\n            if(typeof noCallbacks !== 'undefined'){\r\n                isResizing = false;\r\n            }\r\n\r\n            setScrollingSpeed(originals.scrollingSpeed, 'internal');\r\n        }\r\n\r\n        /**\r\n        * Scrolls silently (with no animation) the page to the given Y position.\r\n        */\r\n        function silentScroll(top){\r\n            // The first section can have a negative value in iOS 10. Not quite sure why: -0.0142822265625\r\n            // that's why we round it to 0.\r\n            var roundedTop = Math.round(top);\r\n\r\n            if (options.css3 && options.autoScrolling && !options.scrollBar){\r\n                var translate3d = 'translate3d(0px, -' + roundedTop + 'px, 0px)';\r\n                transformContainer(translate3d, false);\r\n            }\r\n            else if(options.autoScrolling && !options.scrollBar){\r\n                css(container, {'top': -roundedTop + 'px'});\r\n                FP.test.top = -roundedTop + 'px';\r\n            }\r\n            else{\r\n                var scrollSettings = getScrollSettings(roundedTop);\r\n                setScrolling(scrollSettings.element, scrollSettings.options);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Returns the cross-browser transform string.\r\n        */\r\n        function getTransforms(translate3d){\r\n            return {\r\n                '-webkit-transform': translate3d,\r\n                '-moz-transform': translate3d,\r\n                '-ms-transform':translate3d,\r\n                'transform': translate3d\r\n            };\r\n        }\r\n\r\n        /**\r\n        * Allowing or disallowing the mouse/swipe scroll in a given direction. (not for keyboard)\r\n        * @type  m (mouse) or k (keyboard)\r\n        */\r\n        function setIsScrollAllowed(value, direction, type){\r\n            //up, down, left, right\r\n            if(direction !== 'all'){\r\n                isScrollAllowed[type][direction] = value;\r\n            }\r\n\r\n            //all directions?\r\n            else{\r\n                Object.keys(isScrollAllowed[type]).forEach(function(key){\r\n                    isScrollAllowed[type][key] = value;\r\n                });\r\n            }\r\n        }\r\n\r\n        /*\r\n        * Destroys fullpage.js plugin events and optinally its html markup and styles\r\n        */\r\n        function destroy(all){\r\n            setAutoScrolling(false, 'internal');\r\n            setAllowScrolling(true);\r\n            setMouseHijack(false);\r\n            setKeyboardScrolling(false);\r\n            addClass(container, DESTROYED);\r\n\r\n            [\r\n                afterSlideLoadsId, \r\n                afterSectionLoadsId,\r\n                resizeId,\r\n                scrollId,\r\n                scrollId2,\r\n                g_doubleCheckHeightId,\r\n                resizeHandlerId\r\n            ].forEach(function(timeoutId){\r\n                clearTimeout(timeoutId);\r\n            });\r\n\r\n            window.removeEventListener('scroll', scrollHandler);\r\n            window.removeEventListener('hashchange', hashChangeHandler);\r\n            window.removeEventListener('resize', resizeHandler);\r\n\r\n            document.removeEventListener('keydown', keydownHandler);\r\n            document.removeEventListener('keyup', keyUpHandler);\r\n\r\n            ['click', 'touchstart'].forEach(function(eventName){\r\n                document.removeEventListener(eventName, delegatedEvents);\r\n            });\r\n\r\n            ['mouseenter', 'touchstart', 'mouseleave', 'touchend'].forEach(function(eventName){\r\n                document.removeEventListener(eventName, onMouseEnterOrLeave, true); //true is required!\r\n            });\r\n\r\n            //lets make a mess!\r\n            if(all){\r\n                destroyStructure();\r\n            }\r\n        }\r\n\r\n        /*\r\n        * Removes inline styles added by fullpage.js\r\n        */\r\n        function destroyStructure(){\r\n            //reseting the `top` or `translate` properties to 0\r\n            silentScroll(0);\r\n\r\n            //loading all the lazy load content\r\n            $('img[data-src], source[data-src], audio[data-src], iframe[data-src]', container).forEach(function(item){\r\n                setSrc(item, 'src');\r\n            });\r\n\r\n            $('img[data-srcset]').forEach(function(item){\r\n                setSrc(item, 'srcset');\r\n            });\r\n\r\n            remove($(SECTION_NAV_SEL + ', ' + SLIDES_NAV_SEL +  ', ' + SLIDES_ARROW_SEL));\r\n\r\n            //removing inline styles\r\n            css($(SECTION_SEL), {\r\n                'height': '',\r\n                'background-color' : '',\r\n                'padding': ''\r\n            });\r\n\r\n            css($(SLIDE_SEL), {\r\n                'width': ''\r\n            });\r\n\r\n            css(container, {\r\n                'height': '',\r\n                'position': '',\r\n                '-ms-touch-action': '',\r\n                'touch-action': ''\r\n            });\r\n\r\n            css($htmlBody, {\r\n                'overflow': '',\r\n                'height': ''\r\n            });\r\n\r\n            // remove .fp-enabled class\r\n            removeClass($html, ENABLED);\r\n\r\n            // remove .fp-responsive class\r\n            removeClass($body, RESPONSIVE);\r\n\r\n            // remove all of the .fp-viewing- classes\r\n            $body.className.split(/\\s+/).forEach(function (className) {\r\n                if (className.indexOf(VIEWING_PREFIX) === 0) {\r\n                    removeClass($body, className);\r\n                }\r\n            });\r\n\r\n            //removing added classes\r\n            $(SECTION_SEL + ', ' + SLIDE_SEL).forEach(function(item){\r\n                if(options.scrollOverflowHandler && options.scrollOverflow){\r\n                    options.scrollOverflowHandler.remove(item);\r\n                }\r\n                removeClass(item, TABLE + ' ' + ACTIVE + ' ' + COMPLETELY);\r\n                var previousStyles = item.getAttribute('data-fp-styles');\r\n                if(previousStyles){\r\n                    item.setAttribute('style', item.getAttribute('data-fp-styles'));\r\n                }\r\n\r\n                //removing anchors if they were not set using the HTML markup\r\n                if(hasClass(item, SECTION) && !g_initialAnchorsInDom){\r\n                    item.removeAttribute('data-anchor');\r\n                }\r\n            });\r\n\r\n            //removing the applied transition from the fullpage wrapper\r\n            removeAnimation(container);\r\n\r\n            //Unwrapping content\r\n            [TABLE_CELL_SEL, SLIDES_CONTAINER_SEL,SLIDES_WRAPPER_SEL].forEach(function(selector){\r\n                $(selector, container).forEach(function(item){\r\n                    //unwrap not being use in case there's no child element inside and its just text\r\n                    unwrap(item);\r\n                });\r\n            });\r\n\r\n            //removing the applied transition from the fullpage wrapper\r\n            css(container, {\r\n                '-webkit-transition': 'none',\r\n                'transition': 'none'\r\n            });\r\n\r\n            //scrolling the page to the top with no animation\r\n            window.scrollTo(0, 0);\r\n\r\n            //removing selectors\r\n            var usedSelectors = [SECTION, SLIDE, SLIDES_CONTAINER];\r\n            usedSelectors.forEach(function(item){\r\n                removeClass($('.' + item), item);\r\n            });\r\n        }\r\n\r\n        /*\r\n        * Sets the state for a variable with multiple states (original, and temporal)\r\n        * Some variables such as `autoScrolling` or `recordHistory` might change automatically its state when using `responsive` or `autoScrolling:false`.\r\n        * This function is used to keep track of both states, the original and the temporal one.\r\n        * If type is not 'internal', then we assume the user is globally changing the variable.\r\n        */\r\n        function setVariableState(variable, value, type){\r\n            options[variable] = value;\r\n            if(type !== 'internal'){\r\n                originals[variable] = value;\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Displays warnings\r\n        */\r\n        function displayWarnings(){\r\n            var l = options['li' + 'c' + 'enseK' + 'e' + 'y'];\r\n            var msgStyle = 'font-size: 15px;background:yellow;'\r\n\r\n            if(!isOK){\r\n                showError('error', 'Fullpage.js version 3 has changed its license to GPLv3 and it requires a `licenseKey` option. Read about it here:');\r\n                showError('error', 'https://github.com/alvarotrigo/fullPage.js#options.');\r\n            }\r\n            else if(l && l.length < 20){\r\n                console.warn('%c This website was made using fullPage.js slider. More info on the following website:', msgStyle);\r\n                console.warn('%c https://alvarotrigo.com/fullPage/', msgStyle);\r\n            }\r\n\r\n            if(hasClass($html, ENABLED)){\r\n                showError('error', 'Fullpage.js can only be initialized once and you are doing it multiple times!');\r\n                return;\r\n            }\r\n\r\n            // Disable mutually exclusive settings\r\n            if (options.continuousVertical &&\r\n                (options.loopTop || options.loopBottom)) {\r\n                options.continuousVertical = false;\r\n                showError('warn', 'Option `loopTop/loopBottom` is mutually exclusive with `continuousVertical`; `continuousVertical` disabled');\r\n            }\r\n\r\n            if(options.scrollOverflow &&\r\n               (options.scrollBar || !options.autoScrolling)){\r\n                showError('warn', 'Options scrollBar:true and autoScrolling:false are mutually exclusive with scrollOverflow:true. Sections with scrollOverflow might not work well in Firefox');\r\n            }\r\n\r\n            if(options.continuousVertical && (options.scrollBar || !options.autoScrolling)){\r\n                options.continuousVertical = false;\r\n                showError('warn', 'Scroll bars (`scrollBar:true` or `autoScrolling:false`) are mutually exclusive with `continuousVertical`; `continuousVertical` disabled');\r\n            }\r\n\r\n            if(options.scrollOverflow && options.scrollOverflowHandler == null){\r\n                options.scrollOverflow = false;\r\n                showError('error', 'The option `scrollOverflow:true` requires the file `scrolloverflow.min.js`. Please include it before fullPage.js.');\r\n            }\r\n\r\n            //using extensions? Wrong file!\r\n            extensions.forEach(function(extension){\r\n                //is the option set to true?\r\n                if(options[extension]){\r\n                    showError('warn', 'fullpage.js extensions require fullpage.extensions.min.js file instead of the usual fullpage.js. Requested: '+ extension);\r\n                }\r\n            });\r\n\r\n            //anchors can not have the same value as any element ID or NAME\r\n            options.anchors.forEach(function(name){\r\n\r\n                //case insensitive selectors (http://stackoverflow.com/a/19465187/1081396)\r\n                var nameAttr = [].slice.call($('[name]')).filter(function(item) {\r\n                    return item.getAttribute('name') && item.getAttribute('name').toLowerCase() == name.toLowerCase();\r\n                });\r\n\r\n                var idAttr = [].slice.call($('[id]')).filter(function(item) {\r\n                    return item.getAttribute('id') && item.getAttribute('id').toLowerCase() == name.toLowerCase();\r\n                });\r\n\r\n                if(idAttr.length || nameAttr.length ){\r\n                    showError('error', 'data-anchor tags can not have the same value as any `id` element on the site (or `name` element for IE).');\r\n                    var propertyName = idAttr.length ? 'id' : 'name';\r\n\r\n                    if(idAttr.length || nameAttr.length){\r\n                        showError('error', '\"' + name + '\" is is being used by another element `'+ propertyName +'` property');\r\n                    }\r\n                }\r\n            });\r\n        }\r\n\r\n        /**\r\n        * Getting the position of the element to scroll when using jQuery animations\r\n        */\r\n        function getScrolledPosition(element){\r\n            var position;\r\n\r\n            //is not the window element and is a slide?\r\n            if(element.self != window && hasClass(element, SLIDES_WRAPPER)){\r\n                position = element.scrollLeft;\r\n            }\r\n            else if(!options.autoScrolling  || options.scrollBar){\r\n                position = getScrollTop();\r\n            }\r\n            else{\r\n                position = element.offsetTop;\r\n            }\r\n\r\n            //gets the top property of the wrapper\r\n            return position;\r\n        }\r\n\r\n        /**\r\n        * Simulates the animated scrollTop of jQuery. Used when css3:false or scrollBar:true or autoScrolling:false\r\n        * http://stackoverflow.com/a/16136789/1081396\r\n        */\r\n        function scrollTo(element, to, duration, callback) {\r\n            var start = getScrolledPosition(element);\r\n            var change = to - start;\r\n            var currentTime = 0;\r\n            var increment = 20;\r\n            activeAnimation = true;\r\n\r\n            var animateScroll = function(){\r\n                if(activeAnimation){ //in order to stope it from other function whenever we want\r\n                    var val = to;\r\n\r\n                    currentTime += increment;\r\n\r\n                    if(duration){\r\n                        val = window.fp_easings[options.easing](currentTime, start, change, duration);\r\n                    }\r\n\r\n                    setScrolling(element, val);\r\n\r\n                    if(currentTime < duration) {\r\n                        setTimeout(animateScroll, increment);\r\n                    }else if(typeof callback !== 'undefined'){\r\n                        callback();\r\n                    }\r\n                }else if (currentTime < duration){\r\n                    callback();\r\n                }\r\n            };\r\n\r\n            animateScroll();\r\n        }\r\n\r\n        /**\r\n        * Scrolls the page / slider the given number of pixels.\r\n        * It will do it one or another way dependiong on the library's config.\r\n        */\r\n        function setScrolling(element, val){\r\n            if(!options.autoScrolling || options.scrollBar || (element.self != window && hasClass(element, SLIDES_WRAPPER))){\r\n\r\n                //scrolling horizontally through the slides?\r\n                if(element.self != window  && hasClass(element, SLIDES_WRAPPER)){\r\n                    element.scrollLeft = val;\r\n                }\r\n                //vertical scroll\r\n                else{\r\n                    element.scrollTo(0, val);\r\n                }\r\n            }else{\r\n                 element.style.top = val + 'px';\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Gets the active slide.\r\n        */\r\n        function getActiveSlide(){\r\n            var activeSlide = $(SLIDE_ACTIVE_SEL, $(SECTION_ACTIVE_SEL)[0])[0];\r\n            return nullOrSlide(activeSlide);\r\n        }\r\n\r\n        /**\r\n        * Gets the active section.\r\n        */\r\n        function getActiveSection(){\r\n            return new Section($(SECTION_ACTIVE_SEL)[0]);\r\n        }\r\n\r\n        /**\r\n        * Item. Slide or Section objects share the same properties.\r\n        */\r\n        function Item(el, selector){\r\n            this.anchor = el.getAttribute('data-anchor');\r\n            this.item = el;\r\n            this.index = index(el, selector);\r\n            this.isLast = this.index === el.parentElement.querySelectorAll(selector).length -1;\r\n            this.isFirst = !this.index;\r\n        }\r\n\r\n        /**\r\n        * Section object\r\n        */\r\n        function Section(el){\r\n            Item.call(this, el, SECTION_SEL);\r\n        }\r\n\r\n        /**\r\n        * Slide object\r\n        */\r\n        function Slide(el){\r\n            Item.call(this, el, SLIDE_SEL);\r\n        }\r\n\r\n        return FP;\r\n    } //end of $.fn.fullpage\r\n\r\n    //utils\r\n    /**\r\n    * Shows a message in the console of the given type.\r\n    */\r\n    function showError(type, text){\r\n        window.console && window.console[type] && window.console[type]('fullPage: ' + text);\r\n    }\r\n\r\n    /**\r\n    * Equivalent or jQuery function $().\r\n    */\r\n    function $(selector, context){\r\n        context = arguments.length > 1 ? context : document;\r\n        return context ? context.querySelectorAll(selector) : null;\r\n    }\r\n\r\n    /**\r\n    * Extends a given Object properties and its childs.\r\n    */\r\n    function deepExtend(out) {\r\n        out = out || {};\r\n        for (var i = 1, len = arguments.length; i < len; ++i){\r\n            var obj = arguments[i];\r\n\r\n            if(!obj){\r\n              continue;\r\n            }\r\n\r\n            for(var key in obj){\r\n              if (!obj.hasOwnProperty(key)){\r\n                continue;\r\n              }\r\n\r\n              // based on https://javascriptweblog.wordpress.com/2011/08/08/fixing-the-javascript-typeof-operator/\r\n              if (Object.prototype.toString.call(obj[key]) === '[object Object]'){\r\n                out[key] = deepExtend(out[key], obj[key]);\r\n                continue;\r\n              }\r\n\r\n              out[key] = obj[key];\r\n            }\r\n        }\r\n        return out;\r\n    }\r\n\r\n    /**\r\n    * Checks if the passed element contains the passed class.\r\n    */\r\n    function hasClass(el, className){\r\n        if(el == null){\r\n            return false;\r\n        }\r\n        if (el.classList){\r\n            return el.classList.contains(className);\r\n        }\r\n        return new RegExp('(^| )' + className + '( |$)', 'gi').test(el.className);\r\n    }\r\n\r\n    /**\r\n    * Gets the window height. Crossbrowser.\r\n    */\r\n    function getWindowHeight(){\r\n        return 'innerHeight' in window ? window.innerHeight : document.documentElement.offsetHeight;\r\n    }\r\n\r\n    /**\r\n    * Gets the window width.\r\n    */\r\n    function getWindowWidth(){\r\n        return window.innerWidth;\r\n    }\r\n\r\n    /**\r\n    * Set's the CSS properties for the passed item/s.\r\n    * @param {NodeList|HTMLElement} items\r\n    * @param {Object} props css properties and values.\r\n    */\r\n    function css(items, props) {\r\n        items = getList(items);\r\n\r\n        var key;\r\n        for (key in props) {\r\n            if (props.hasOwnProperty(key)) {\r\n                if (key !== null) {\r\n                    for (var i = 0; i < items.length; i++) {\r\n                        var item = items[i];\r\n                        item.style[key] = props[key];\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return items;\r\n    }\r\n\r\n    /**\r\n    * Generic function to get the previous or next element.\r\n    */\r\n    function until(item, selector, fn){\r\n        var sibling = item[fn];\r\n        while(sibling && !matches(sibling, selector)){\r\n            sibling = sibling[fn];\r\n        }\r\n\r\n        return sibling;\r\n    }\r\n\r\n    /**\r\n    * Gets the previous element to the passed element that matches the passed selector.\r\n    */\r\n    function prevUntil(item, selector){\r\n        return until(item, selector, 'previousElementSibling');\r\n    }\r\n\r\n    /**\r\n    * Gets the next element to the passed element that matches the passed selector.\r\n    */\r\n    function nextUntil(item, selector){\r\n        return until(item, selector, 'nextElementSibling');\r\n    }\r\n\r\n    /**\r\n    * Gets the previous element to the passed element.\r\n    */\r\n    function prev(item){\r\n        return item.previousElementSibling;\r\n    }\r\n\r\n    /**\r\n    * Gets the next element to the passed element.\r\n    */\r\n    function next(item){\r\n        return item.nextElementSibling;\r\n    }\r\n\r\n    /**\r\n    * Gets the last element from the passed list of elements.\r\n    */\r\n    function last(item){\r\n        return item[item.length-1];\r\n    }\r\n\r\n    /**\r\n    * Gets index from the passed element.\r\n    * @param {String} selector is optional.\r\n    */\r\n    function index(item, selector) {\r\n        item = isArrayOrList(item) ? item[0] : item;\r\n        var children = selector != null? $(selector, item.parentNode) : item.parentNode.childNodes;\r\n        var num = 0;\r\n        for (var i=0; i<children.length; i++) {\r\n             if (children[i] == item) return num;\r\n             if (children[i].nodeType==1) num++;\r\n        }\r\n        return -1;\r\n    }\r\n\r\n    /**\r\n    * Gets an iterable element for the passed element/s\r\n    */\r\n    function getList(item){\r\n        return !isArrayOrList(item) ? [item] : item;\r\n    }\r\n\r\n    /**\r\n    * Adds the display=none property for the passed element/s\r\n    */\r\n    function hide(el){\r\n        el = getList(el);\r\n\r\n        for(var i = 0; i<el.length; i++){\r\n            el[i].style.display = 'none';\r\n        }\r\n        return el;\r\n    }\r\n\r\n    /**\r\n    * Adds the display=block property for the passed element/s\r\n    */\r\n    function show(el){\r\n        el = getList(el);\r\n\r\n        for(var i = 0; i<el.length; i++){\r\n            el[i].style.display = 'block';\r\n        }\r\n        return el;\r\n    }\r\n\r\n    /**\r\n    * Checks if the passed element is an iterable element or not\r\n    */\r\n    function isArrayOrList(el){\r\n        return Object.prototype.toString.call( el ) === '[object Array]' ||\r\n            Object.prototype.toString.call( el ) === '[object NodeList]';\r\n    }\r\n\r\n    /**\r\n    * Adds the passed class to the passed element/s\r\n    */\r\n    function addClass(el, className) {\r\n        el = getList(el);\r\n\r\n        for(var i = 0; i<el.length; i++){\r\n            var item = el[i];\r\n            if (item.classList){\r\n                item.classList.add(className);\r\n            }\r\n            else{\r\n              item.className += ' ' + className;\r\n            }\r\n        }\r\n        return el;\r\n    }\r\n\r\n    /**\r\n    * Removes the passed class to the passed element/s\r\n    * @param {String} `className` can be multiple classnames separated by whitespace\r\n    */\r\n    function removeClass(el, className){\r\n        el = getList(el);\r\n\r\n        var classNames = className.split(' ');\r\n\r\n        for(var a = 0; a<classNames.length; a++){\r\n            className = classNames[a];\r\n            for(var i = 0; i<el.length; i++){\r\n                var item = el[i];\r\n                if (item.classList){\r\n                    item.classList.remove(className);\r\n                }\r\n                else{\r\n                    item.className = item.className.replace(new RegExp('(^|\\\\b)' + className.split(' ').join('|') + '(\\\\b|$)', 'gi'), ' ');\r\n                }\r\n            }\r\n        }\r\n        return el;\r\n    }\r\n\r\n    /**\r\n    * Appends the given element ot the given parent.\r\n    */\r\n    function appendTo(el, parent){\r\n        parent.appendChild(el);\r\n    }\r\n\r\n    /**\r\n    Usage:\r\n\r\n    var wrapper = document.createElement('div');\r\n    wrapper.className = 'fp-slides';\r\n    wrap($('.slide'), wrapper);\r\n\r\n    https://jsfiddle.net/qwzc7oy3/15/ (vanilla)\r\n    https://jsfiddle.net/oya6ndka/1/ (jquery equivalent)\r\n    */\r\n    function wrap(toWrap, wrapper, isWrapAll) {\r\n        var newParent;\r\n        wrapper = wrapper || document.createElement('div');\r\n        for(var i = 0; i < toWrap.length; i++){\r\n            var item = toWrap[i];\r\n            if(isWrapAll && !i || !isWrapAll){\r\n                newParent = wrapper.cloneNode(true);\r\n                item.parentNode.insertBefore(newParent, item);\r\n            }\r\n            newParent.appendChild(item);\r\n        }\r\n        return toWrap;\r\n    }\r\n\r\n    /**\r\n    Usage:\r\n    var wrapper = document.createElement('div');\r\n    wrapper.className = 'fp-slides';\r\n    wrap($('.slide'), wrapper);\r\n\r\n    https://jsfiddle.net/qwzc7oy3/27/ (vanilla)\r\n    https://jsfiddle.net/oya6ndka/4/ (jquery equivalent)\r\n    */\r\n    function wrapAll(toWrap, wrapper) {\r\n        wrap(toWrap, wrapper, true);\r\n    }\r\n\r\n    /**\r\n    * Usage:\r\n    * wrapInner(document.querySelector('#pepe'), '<div class=\"test\">afdas</div>');\r\n    * wrapInner(document.querySelector('#pepe'), element);\r\n    *\r\n    * https://jsfiddle.net/zexxz0tw/6/\r\n    *\r\n    * https://stackoverflow.com/a/21817590/1081396\r\n    */\r\n    function wrapInner(parent, wrapper) {\r\n        if (typeof wrapper === \"string\"){\r\n            wrapper = createElementFromHTML(wrapper);\r\n        }\r\n\r\n        parent.appendChild(wrapper);\r\n\r\n        while(parent.firstChild !== wrapper){\r\n            wrapper.appendChild(parent.firstChild);\r\n       }\r\n    }\r\n\r\n    /**\r\n    * Usage:\r\n    * unwrap(document.querySelector('#pepe'));\r\n    * unwrap(element);\r\n    *\r\n    * https://jsfiddle.net/szjt0hxq/1/\r\n    *\r\n    */\r\n    function unwrap(wrapper) {\r\n        var wrapperContent = document.createDocumentFragment();\r\n        while (wrapper.firstChild) {\r\n            wrapperContent.appendChild(wrapper.firstChild);\r\n        }\r\n\r\n        wrapper.parentNode.replaceChild(wrapperContent, wrapper);\r\n    }\r\n\r\n    /**\r\n    * http://stackoverflow.com/questions/22100853/dom-pure-javascript-solution-to-jquery-closest-implementation\r\n    * Returns the element or `false` if there's none\r\n    */\r\n    function closest(el, selector) {\r\n        if(el && el.nodeType === 1){\r\n            if(matches(el, selector)){\r\n                return el;\r\n            }\r\n            return closest(el.parentNode, selector);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n    * Places one element (rel) after another one or group of them (reference).\r\n    * @param {HTMLElement} reference\r\n    * @param {HTMLElement|NodeList|String} el\r\n    * https://jsfiddle.net/9s97hhzv/1/\r\n    */\r\n    function after(reference, el) {\r\n        insertBefore(reference, reference.nextSibling, el);\r\n    }\r\n\r\n    /**\r\n    * Places one element (rel) before another one or group of them (reference).\r\n    * @param {HTMLElement} reference\r\n    * @param {HTMLElement|NodeList|String} el\r\n    * https://jsfiddle.net/9s97hhzv/1/\r\n    */\r\n    function before(reference, el) {\r\n        insertBefore(reference, reference, el);\r\n    }\r\n\r\n    /**\r\n    * Based in https://stackoverflow.com/a/19316024/1081396\r\n    * and https://stackoverflow.com/a/4793630/1081396\r\n    */\r\n    function insertBefore(reference, beforeElement, el){\r\n        if(!isArrayOrList(el)){\r\n            if(typeof el == 'string'){\r\n                el = createElementFromHTML(el);\r\n            }\r\n            el = [el];\r\n        }\r\n\r\n        for(var i = 0; i<el.length; i++){\r\n            reference.parentNode.insertBefore(el[i], beforeElement);\r\n        }\r\n    }\r\n\r\n    //http://stackoverflow.com/questions/3464876/javascript-get-window-x-y-position-for-scroll\r\n    function getScrollTop(){\r\n        var doc = document.documentElement;\r\n        return (window.pageYOffset || doc.scrollTop)  - (doc.clientTop || 0);\r\n    }\r\n\r\n    /**\r\n    * Gets the siblings of the passed element\r\n    */\r\n    function siblings(el){\r\n        return Array.prototype.filter.call(el.parentNode.children, function(child){\r\n          return child !== el;\r\n        });\r\n    }\r\n\r\n    //for IE 9 ?\r\n    function preventDefault(event){\r\n        if(event.preventDefault){\r\n            event.preventDefault();\r\n        }\r\n        else{\r\n            event.returnValue = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n    * Determines whether the passed item is of function type.\r\n    */\r\n    function isFunction(item) {\r\n      if (typeof item === 'function') {\r\n        return true;\r\n      }\r\n      var type = Object.prototype.toString(item);\r\n      return type === '[object Function]' || type === '[object GeneratorFunction]';\r\n    }\r\n\r\n    /**\r\n    * Trigger custom events\r\n    */\r\n    function trigger(el, eventName, data){\r\n        var event;\r\n        data = typeof data === 'undefined' ? {} : data;\r\n\r\n        // Native\r\n        if(typeof window.CustomEvent === \"function\" ){\r\n            event = new CustomEvent(eventName, {detail: data});\r\n        }\r\n        else{\r\n            event = document.createEvent('CustomEvent');\r\n            event.initCustomEvent(eventName, true, true, data);\r\n        }\r\n\r\n        el.dispatchEvent(event);\r\n    }\r\n\r\n    /**\r\n    * Polyfill of .matches()\r\n    */\r\n    function matches(el, selector) {\r\n        return (el.matches || el.matchesSelector || el.msMatchesSelector || el.mozMatchesSelector || el.webkitMatchesSelector || el.oMatchesSelector).call(el, selector);\r\n    }\r\n\r\n    /**\r\n    * Toggles the visibility of the passed element el.\r\n    */\r\n    function toggle(el, value){\r\n        if(typeof value === \"boolean\"){\r\n            for(var i = 0; i<el.length; i++){\r\n                el[i].style.display = value ? 'block' : 'none';\r\n            }\r\n        }\r\n        //we don't use it in other way, so no else :)\r\n\r\n        return el;\r\n    }\r\n\r\n    /**\r\n    * Creates a HTMLElement from the passed HTML string.\r\n    * https://stackoverflow.com/a/494348/1081396\r\n    */\r\n    function createElementFromHTML(htmlString) {\r\n        var div = document.createElement('div');\r\n        div.innerHTML = htmlString.trim();\r\n\r\n        // Change this to div.childNodes to support multiple top-level nodes\r\n        return div.firstChild;\r\n    }\r\n\r\n    /**\r\n    * Removes the passed item/s from the DOM.\r\n    */\r\n    function remove(items){\r\n        items = getList(items);\r\n        for(var i = 0; i<items.length; i++){\r\n            var item = items[i];\r\n            if(item && item.parentElement) {\r\n                item.parentNode.removeChild(item);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n    * Filters an array by the passed filter funtion.\r\n    */\r\n    function filter(el, filterFn){\r\n        Array.prototype.filter.call(el, filterFn);\r\n    }\r\n\r\n    //https://jsfiddle.net/w1rktecz/\r\n    function untilAll(item, selector, fn){\r\n        var sibling = item[fn];\r\n        var siblings = [];\r\n        while(sibling){\r\n            if(matches(sibling, selector) || selector == null) {\r\n                siblings.push(sibling);\r\n            }\r\n            sibling = sibling[fn];\r\n        }\r\n\r\n        return siblings;\r\n    }\r\n\r\n    /**\r\n    * Gets all next elements matching the passed selector.\r\n    */\r\n    function nextAll(item, selector){\r\n        return untilAll(item, selector, 'nextElementSibling');\r\n    }\r\n\r\n    /**\r\n    * Gets all previous elements matching the passed selector.\r\n    */\r\n    function prevAll(item, selector){\r\n        return untilAll(item, selector, 'previousElementSibling');\r\n    }\r\n\r\n    /**\r\n    * Converts an object to an array.\r\n    */\r\n    function toArray(objectData){\r\n        return Object.keys(objectData).map(function(key) {\r\n           return objectData[key];\r\n        });\r\n    }\r\n\r\n    /**\r\n    * forEach polyfill for IE\r\n    * https://developer.mozilla.org/en-US/docs/Web/API/NodeList/forEach#Browser_Compatibility\r\n    */\r\n    if (window.NodeList && !NodeList.prototype.forEach) {\r\n        NodeList.prototype.forEach = function (callback, thisArg) {\r\n            thisArg = thisArg || window;\r\n            for (var i = 0; i < this.length; i++) {\r\n                callback.call(thisArg, this[i], i, this);\r\n            }\r\n        };\r\n    }\r\n\r\n    //utils are public, so we can use it wherever we want\r\n    window.fp_utils = {\r\n        $: $,\r\n        deepExtend: deepExtend,\r\n        hasClass: hasClass,\r\n        getWindowHeight: getWindowHeight,\r\n        css: css,\r\n        until: until,\r\n        prevUntil: prevUntil,\r\n        nextUntil: nextUntil,\r\n        prev: prev,\r\n        next: next,\r\n        last: last,\r\n        index: index,\r\n        getList: getList,\r\n        hide: hide,\r\n        show: show,\r\n        isArrayOrList: isArrayOrList,\r\n        addClass: addClass,\r\n        removeClass: removeClass,\r\n        appendTo: appendTo,\r\n        wrap: wrap,\r\n        wrapAll: wrapAll,\r\n        wrapInner: wrapInner,\r\n        unwrap: unwrap,\r\n        closest: closest,\r\n        after: after,\r\n        before: before,\r\n        insertBefore: insertBefore,\r\n        getScrollTop: getScrollTop,\r\n        siblings: siblings,\r\n        preventDefault: preventDefault,\r\n        isFunction: isFunction,\r\n        trigger: trigger,\r\n        matches: matches,\r\n        toggle: toggle,\r\n        createElementFromHTML: createElementFromHTML,\r\n        remove: remove,\r\n        filter: filter,\r\n        untilAll: untilAll,\r\n        nextAll: nextAll,\r\n        prevAll: prevAll,\r\n        showError: showError\r\n    };\r\n\r\n    return initialise;\r\n}));\r\n\r\n/**\r\n * jQuery adapter for fullPage.js 3.0.0\r\n */\r\nif(window.jQuery && window.fullpage){\r\n    (function ($, fullpage) {\r\n        'use strict';\r\n\r\n        // No jQuery No Go\r\n        if (!$ || !fullpage) {\r\n            window.fp_utils.showError('error', 'jQuery is required to use the jQuery fullpage adapter!');\r\n            return;\r\n        }\r\n\r\n        $.fn.fullpage = function(options) {\r\n            options = $.extend({}, options, {'$': $});\r\n            new fullpage(this[0], options);\r\n        };\r\n    })(window.jQuery, window.fullpage);\r\n}\r\n"]}