{"cells": [{"cell_type": "code", "execution_count": 1, "id": "67fcd432-a751-40ce-8375-05646e7ff2df", "metadata": {}, "outputs": [], "source": ["import pandas as pd \n", "df = pd.read_csv(\"statlog_german_credit_data.csv\",encoding=\"utf-8\")\n", "df=df.rename(columns={'Attribute1':'Status_of_existing_checking_account','Attribute2':'Duration','Attribute3':'Credit_history','Attribute4':'Purpose',\n", "                     'Attribute5':'Credit_amount','Attribute6':'Savings_account/bonds','Attribute7':'Present_employment_since',\n", "                      'Attribute8':'Installment_rate_in_percentage_of_disposable_income',\n", "                     'Attribute9':'Personal_status_and_sex','Attribute10':'Other_debtors/guarantors','Attribute11':'Present_residence_since',\n", "                     'Attribute12':'Property','Attribute13':'Age','Attribute14':'Other_installment_plans','Attribute15':'Housing','Attribute16':'Number_of_existing_credits_at_this_bank',\n", "                     'Attribute17':'Job','Attribute18':'Number_of_people_being_liable_to_provide_maintenance_for','Attribute19':'Telephone','Attribute20':'foreign_worker'})\n", "df['class']= list(map(lambda x: x - 1, df['class'].tolist()))"]}, {"cell_type": "code", "execution_count": 2, "id": "0521e186-88b5-46ae-9d65-9af0ae233134", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Status_of_existing_checking_account', 'Duration', 'Credit_history',\n", "       'Purpose', 'Credit_amount', 'Savings_account/bonds',\n", "       'Present_employment_since',\n", "       'Installment_rate_in_percentage_of_disposable_income',\n", "       'Personal_status_and_sex', 'Other_debtors/guarantors',\n", "       'Present_residence_since', 'Property', 'Age', 'Other_installment_plans',\n", "       'Housing', 'Number_of_existing_credits_at_this_bank', 'Job',\n", "       'Number_of_people_being_liable_to_provide_maintenance_for', 'Telephone',\n", "       'foreign_worker', 'class'],\n", "      dtype='object')"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 3, "id": "06dc805a-b030-4b04-97d5-f91f3c4edc00", "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split\n", "X = df.drop(columns='class')\n", "y = list(map(lambda x: x - 1, df['class'].tolist()))\n", "X_train, X_test,y_train, y_test = train_test_split(X,y ,random_state=104, test_size=0.25, shuffle=True)"]}, {"cell_type": "code", "execution_count": 4, "id": "42c6fb42-7614-4d46-86c9-4bbd7a271ab5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [1.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [1.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [1.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [1.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [1.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 1.0, 0.0]]\n", "[['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A172'], ['A173'], ['A174'], ['A172'], ['A174'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A172'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A172'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A174'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A172'], ['A173'], ['A172'], ['A174'], ['A173'], ['A171'], ['A172'], ['A173'], ['A172'], ['A174'], ['A173'], ['A173'], ['A173'], ['A172'], ['A174'], ['A173'], ['A174'], ['A172'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A174'], ['A172'], ['A174'], ['A174'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A172'], ['A172'], ['A172'], ['A174'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A174'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A174'], ['A174'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A174'], ['A173'], ['A174'], ['A172'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A172'], ['A171'], ['A174'], ['A174'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A172'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A174'], ['A172'], ['A173'], ['A173'], ['A173'], ['A174'], ['A171'], ['A173'], ['A173'], ['A174'], ['A172'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A172'], ['A173'], ['A173'], ['A172'], ['A174'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A172'], ['A173'], ['A174'], ['A173'], ['A172'], ['A173'], ['A173'], ['A172'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A172'], ['A172'], ['A174'], ['A171'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A172'], ['A173'], ['A173'], ['A173'], ['A172'], ['A172'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A174'], ['A172'], ['A172'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A172'], ['A173'], ['A173'], ['A172'], ['A173'], ['A174'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A172'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A174'], ['A174'], ['A173'], ['A174'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A171'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A172'], ['A172'], ['A173'], ['A172'], ['A171'], ['A173'], ['A173'], ['A172'], ['A172'], ['A173'], ['A172'], ['A172'], ['A173'], ['A172'], ['A174'], ['A173'], ['A174'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A174'], ['A172'], ['A173'], ['A174'], ['A172'], ['A173'], ['A173'], ['A172'], ['A173'], ['A172'], ['A173'], ['A174'], ['A172'], ['A172'], ['A173'], ['A171'], ['A172'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A172'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A171'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A174'], ['A174'], ['A173'], ['A173'], ['A173'], ['A174'], ['A172'], ['A173'], ['A174'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A174'], ['A172'], ['A174'], ['A173'], ['A174'], ['A173'], ['A174'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A174'], ['A174'], ['A172'], ['A173'], ['A173'], ['A172'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A172'], ['A174'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A171'], ['A172'], ['A174'], ['A174'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A171'], ['A171'], ['A174'], ['A173'], ['A173'], ['A172'], ['A174'], ['A172'], ['A173'], ['A173'], ['A174'], ['A173'], ['A174'], ['A173'], ['A173'], ['A174'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A172'], ['A174'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A172'], ['A173'], ['A174'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A174'], ['A173'], ['A174'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A172'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A174'], ['A172'], ['A173'], ['A173'], ['A173'], ['A172'], ['A172'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A172'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A174'], ['A172'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A172'], ['A173'], ['A172'], ['A174'], ['A173'], ['A173'], ['A172'], ['A172'], ['A173'], ['A172'], ['A173'], ['A172'], ['A172'], ['A174'], ['A172'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A174'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A174'], ['A174'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A171'], ['A172'], ['A174'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A174'], ['A172'], ['A173'], ['A174'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A174'], ['A172'], ['A174'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A174'], ['A173'], ['A172'], ['A174'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A171'], ['A173'], ['A174'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A174'], ['A173'], ['A174'], ['A172'], ['A174'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A174'], ['A173'], ['A172'], ['A172'], ['A173'], ['A172'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A172'], ['A173'], ['A173'], ['A171'], ['A174'], ['A172'], ['A174'], ['A172'], ['A173'], ['A172'], ['A173'], ['A173'], ['A174'], ['A172'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A172'], ['A172'], ['A173'], ['A173'], ['A173'], ['A171'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A174'], ['A173'], ['A172'], ['A172'], ['A173'], ['A173'], ['A172'], ['A173'], ['A174'], ['A174'], ['A173'], ['A171'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A172'], ['A172'], ['A174'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A171'], ['A173'], ['A172'], ['A173'], ['A173'], ['A171'], ['A173'], ['A172'], ['A173'], ['A174'], ['A171'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A174'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A174'], ['A173'], ['A174'], ['A173'], ['A174'], ['A173'], ['A173'], ['A171'], ['A173'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A174'], ['A173'], ['A172'], ['A173'], ['A173'], ['A174'], ['A174'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A174'], ['A174'], ['A172'], ['A173'], ['A172'], ['A172'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A174'], ['A173'], ['A174'], ['A173'], ['A174'], ['A173'], ['A174'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A174'], ['A172'], ['A174'], ['A173'], ['A174'], ['A174'], ['A172'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A174'], ['A172'], ['A173'], ['A171'], ['A173'], ['A173'], ['A174'], ['A173'], ['A174'], ['A172'], ['A172'], ['A172'], ['A173'], ['A173'], ['A173'], ['A173'], ['A173'], ['A172'], ['A173'], ['A172'], ['A174'], ['A173'], ['A172'], ['A173'], ['A172'], ['A171'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A172'], ['A173'], ['A173'], ['A174'], ['A174'], ['A173'], ['A172'], ['A173'], ['A173'], ['A173'], ['A174'], ['A172'], ['A172'], ['A172'], ['A172'], ['A174'], ['A173'], ['A172'], ['A174'], ['A173'], ['A173'], ['A173']]\n"]}], "source": ["from sklearn.preprocessing import OneHotEncoder\n", "Job = df['Job'].values\n", "encoder = OneHotEncoder(sparse_output = False)\n", "encoder_Job = encoder.fit_transform(Job.reshape((-1,1)))\n", "print(encoder_Job.tolist())\n", "decoded_Job = encoder.inverse_transform(encoder_Job)\n", "print(decoded_Job.tolist())"]}, {"cell_type": "code", "execution_count": 5, "id": "b3892e18-69bf-408a-a4d1-dd3c746c040b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Job_A171</th>\n", "      <th>Job_A172</th>\n", "      <th>Job_A173</th>\n", "      <th>Job_A174</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>995</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>996</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>997</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>998</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>999</th>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1000 rows × 4 columns</p>\n", "</div>"], "text/plain": ["     Job_A171  Job_A172  Job_A173  Job_A174\n", "0       False     False      True     False\n", "1       False     False      True     False\n", "2       False      True     False     False\n", "3       False     False      True     False\n", "4       False     False      True     False\n", "..        ...       ...       ...       ...\n", "995     False      True     False     False\n", "996     False     False     False      True\n", "997     False     False      True     False\n", "998     False     False      True     False\n", "999     False     False      True     False\n", "\n", "[1000 rows x 4 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["dummy_job = pd.get_dummies(Job,prefix='Job',prefix_sep='_',dummy_na=False,columns=None,sparse=False,drop_first=False,dtype=None)\n", "dummy_job"]}, {"cell_type": "code", "execution_count": 6, "id": "a7484585-d488-4e11-b7e3-378a9dd6d7af", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['A171', 'A172', 'A173', 'A174']\n", "[2, 2, 1, 2, 2, 1, 2, 3, 1, 3, 2, 2, 2, 1, 2, 1, 2, 2, 3, 2, 2, 2, 1, 2, 2, 1, 1, 2, 2, 2, 2, 2, 2, 1, 3, 1, 2, 2, 2, 2, 3, 2, 1, 2, 1, 3, 2, 0, 1, 2, 1, 3, 2, 2, 2, 1, 3, 2, 3, 1, 2, 2, 3, 2, 2, 2, 2, 2, 2, 2, 2, 1, 3, 1, 3, 3, 2, 2, 1, 2, 2, 2, 1, 1, 1, 3, 2, 2, 3, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 3, 1, 2, 2, 2, 2, 3, 3, 2, 2, 2, 2, 2, 1, 2, 2, 2, 3, 2, 2, 3, 2, 3, 1, 2, 2, 2, 1, 2, 3, 2, 2, 2, 1, 2, 2, 2, 2, 1, 2, 1, 0, 3, 3, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 2, 2, 1, 2, 1, 2, 2, 2, 3, 2, 2, 2, 2, 2, 2, 2, 2, 3, 2, 2, 3, 2, 2, 3, 2, 2, 3, 1, 2, 2, 2, 3, 0, 2, 2, 3, 1, 2, 2, 2, 3, 2, 2, 2, 3, 2, 1, 2, 2, 1, 3, 2, 2, 1, 2, 2, 1, 2, 3, 2, 1, 2, 2, 1, 2, 1, 2, 2, 2, 2, 2, 2, 3, 2, 2, 2, 1, 2, 1, 1, 3, 0, 1, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 1, 1, 2, 2, 2, 1, 1, 2, 2, 1, 2, 2, 3, 1, 1, 2, 2, 2, 3, 2, 2, 2, 3, 2, 1, 2, 2, 1, 2, 3, 2, 2, 1, 2, 2, 1, 2, 3, 2, 2, 2, 3, 3, 2, 3, 2, 2, 1, 2, 2, 0, 2, 1, 2, 2, 2, 1, 1, 2, 1, 0, 2, 2, 1, 1, 2, 1, 1, 2, 1, 3, 2, 3, 2, 2, 1, 2, 2, 2, 2, 3, 2, 3, 1, 2, 3, 1, 2, 2, 1, 2, 1, 2, 3, 1, 1, 2, 0, 1, 2, 2, 2, 3, 2, 2, 1, 2, 3, 2, 2, 2, 3, 2, 2, 0, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 2, 2, 2, 3, 1, 2, 3, 2, 2, 1, 2, 2, 3, 2, 2, 3, 1, 3, 2, 3, 2, 3, 2, 2, 1, 2, 2, 2, 1, 2, 2, 2, 2, 3, 2, 2, 3, 3, 1, 2, 2, 1, 2, 3, 2, 2, 2, 1, 3, 2, 2, 2, 2, 2, 0, 1, 3, 3, 2, 2, 2, 1, 2, 0, 0, 3, 2, 2, 1, 3, 1, 2, 2, 3, 2, 3, 2, 2, 3, 2, 3, 2, 2, 2, 2, 2, 2, 3, 2, 2, 2, 1, 2, 2, 1, 2, 2, 1, 3, 2, 2, 2, 2, 1, 2, 1, 2, 2, 2, 2, 3, 2, 2, 1, 2, 2, 3, 2, 2, 1, 2, 3, 2, 1, 2, 2, 2, 2, 2, 2, 1, 2, 3, 2, 3, 2, 3, 2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 1, 2, 2, 1, 2, 1, 2, 2, 2, 2, 2, 2, 3, 2, 3, 1, 2, 2, 2, 1, 1, 2, 2, 2, 1, 2, 2, 1, 2, 2, 3, 2, 2, 2, 2, 1, 2, 1, 2, 2, 3, 2, 2, 2, 2, 2, 1, 2, 3, 1, 1, 2, 2, 2, 2, 1, 2, 1, 2, 1, 3, 2, 2, 1, 1, 2, 1, 2, 1, 1, 3, 1, 2, 1, 2, 2, 2, 2, 1, 3, 2, 2, 3, 2, 2, 2, 2, 1, 2, 2, 2, 3, 3, 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 0, 1, 3, 2, 3, 2, 2, 2, 2, 1, 3, 1, 2, 3, 2, 2, 1, 2, 2, 2, 2, 2, 2, 3, 1, 3, 2, 2, 2, 2, 1, 2, 3, 2, 1, 3, 2, 2, 1, 2, 2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 1, 2, 1, 2, 2, 2, 0, 2, 3, 1, 2, 2, 2, 2, 2, 2, 2, 2, 1, 3, 2, 3, 1, 3, 2, 2, 3, 2, 2, 3, 2, 1, 1, 2, 1, 1, 2, 2, 2, 2, 1, 1, 2, 2, 0, 3, 1, 3, 1, 2, 1, 2, 2, 3, 1, 2, 1, 2, 2, 2, 1, 1, 2, 2, 2, 0, 2, 2, 2, 2, 2, 1, 3, 2, 1, 1, 2, 2, 1, 2, 3, 3, 2, 0, 2, 2, 2, 3, 2, 2, 2, 1, 1, 3, 1, 2, 2, 2, 2, 2, 3, 2, 2, 2, 2, 2, 1, 2, 0, 2, 1, 2, 2, 0, 2, 1, 2, 3, 0, 2, 1, 2, 2, 2, 2, 2, 2, 3, 2, 2, 2, 2, 1, 3, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 2, 2, 1, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 2, 3, 2, 3, 2, 3, 2, 2, 0, 2, 2, 2, 2, 3, 2, 3, 2, 1, 2, 2, 3, 3, 2, 1, 2, 2, 2, 2, 2, 3, 3, 1, 2, 1, 1, 2, 1, 2, 2, 2, 3, 2, 3, 2, 3, 2, 3, 2, 2, 2, 2, 2, 2, 2, 1, 1, 2, 2, 2, 2, 3, 1, 3, 2, 3, 3, 1, 2, 1, 2, 2, 2, 3, 1, 2, 0, 2, 2, 3, 2, 3, 1, 1, 1, 2, 2, 2, 2, 2, 1, 2, 1, 3, 2, 1, 2, 1, 0, 2, 2, 1, 2, 2, 1, 2, 2, 3, 3, 2, 1, 2, 2, 2, 3, 1, 1, 1, 1, 3, 2, 1, 3, 2, 2, 2]\n", "['A173', 'A173', 'A172', 'A173', 'A173', 'A172', 'A173', 'A174', 'A172', 'A174', 'A173', 'A173', 'A173', 'A172', 'A173', 'A172', 'A173', 'A173', 'A174', 'A173', 'A173', 'A173', 'A172', 'A173', 'A173', 'A172', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A174', 'A172', 'A173', 'A173', 'A173', 'A173', 'A174', 'A173', 'A172', 'A173', 'A172', 'A174', 'A173', 'A171', 'A172', 'A173', 'A172', 'A174', 'A173', 'A173', 'A173', 'A172', 'A174', 'A173', 'A174', 'A172', 'A173', 'A173', 'A174', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A174', 'A172', 'A174', 'A174', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A172', 'A172', 'A172', 'A174', 'A173', 'A173', 'A174', 'A173', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A174', 'A172', 'A173', 'A173', 'A173', 'A173', 'A174', 'A174', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A174', 'A173', 'A173', 'A174', 'A173', 'A174', 'A172', 'A173', 'A173', 'A173', 'A172', 'A173', 'A174', 'A173', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A172', 'A171', 'A174', 'A174', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A174', 'A173', 'A173', 'A172', 'A173', 'A172', 'A173', 'A173', 'A173', 'A174', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A174', 'A173', 'A173', 'A174', 'A173', 'A173', 'A174', 'A173', 'A173', 'A174', 'A172', 'A173', 'A173', 'A173', 'A174', 'A171', 'A173', 'A173', 'A174', 'A172', 'A173', 'A173', 'A173', 'A174', 'A173', 'A173', 'A173', 'A174', 'A173', 'A172', 'A173', 'A173', 'A172', 'A174', 'A173', 'A173', 'A172', 'A173', 'A173', 'A172', 'A173', 'A174', 'A173', 'A172', 'A173', 'A173', 'A172', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A174', 'A173', 'A173', 'A173', 'A172', 'A173', 'A172', 'A172', 'A174', 'A171', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A172', 'A173', 'A173', 'A173', 'A172', 'A172', 'A173', 'A173', 'A172', 'A173', 'A173', 'A174', 'A172', 'A172', 'A173', 'A173', 'A173', 'A174', 'A173', 'A173', 'A173', 'A174', 'A173', 'A172', 'A173', 'A173', 'A172', 'A173', 'A174', 'A173', 'A173', 'A172', 'A173', 'A173', 'A172', 'A173', 'A174', 'A173', 'A173', 'A173', 'A174', 'A174', 'A173', 'A174', 'A173', 'A173', 'A172', 'A173', 'A173', 'A171', 'A173', 'A172', 'A173', 'A173', 'A173', 'A172', 'A172', 'A173', 'A172', 'A171', 'A173', 'A173', 'A172', 'A172', 'A173', 'A172', 'A172', 'A173', 'A172', 'A174', 'A173', 'A174', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A174', 'A173', 'A174', 'A172', 'A173', 'A174', 'A172', 'A173', 'A173', 'A172', 'A173', 'A172', 'A173', 'A174', 'A172', 'A172', 'A173', 'A171', 'A172', 'A173', 'A173', 'A173', 'A174', 'A173', 'A173', 'A172', 'A173', 'A174', 'A173', 'A173', 'A173', 'A174', 'A173', 'A173', 'A171', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A174', 'A174', 'A173', 'A173', 'A173', 'A174', 'A172', 'A173', 'A174', 'A173', 'A173', 'A172', 'A173', 'A173', 'A174', 'A173', 'A173', 'A174', 'A172', 'A174', 'A173', 'A174', 'A173', 'A174', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A174', 'A173', 'A173', 'A174', 'A174', 'A172', 'A173', 'A173', 'A172', 'A173', 'A174', 'A173', 'A173', 'A173', 'A172', 'A174', 'A173', 'A173', 'A173', 'A173', 'A173', 'A171', 'A172', 'A174', 'A174', 'A173', 'A173', 'A173', 'A172', 'A173', 'A171', 'A171', 'A174', 'A173', 'A173', 'A172', 'A174', 'A172', 'A173', 'A173', 'A174', 'A173', 'A174', 'A173', 'A173', 'A174', 'A173', 'A174', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A174', 'A173', 'A173', 'A173', 'A172', 'A173', 'A173', 'A172', 'A173', 'A173', 'A172', 'A174', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A174', 'A173', 'A173', 'A172', 'A173', 'A173', 'A174', 'A173', 'A173', 'A172', 'A173', 'A174', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A174', 'A173', 'A174', 'A173', 'A174', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A173', 'A172', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A174', 'A173', 'A174', 'A172', 'A173', 'A173', 'A173', 'A172', 'A172', 'A173', 'A173', 'A173', 'A172', 'A173', 'A173', 'A172', 'A173', 'A173', 'A174', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A172', 'A173', 'A173', 'A174', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A174', 'A172', 'A172', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A172', 'A173', 'A172', 'A174', 'A173', 'A173', 'A172', 'A172', 'A173', 'A172', 'A173', 'A172', 'A172', 'A174', 'A172', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A172', 'A174', 'A173', 'A173', 'A174', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A174', 'A174', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A171', 'A172', 'A174', 'A173', 'A174', 'A173', 'A173', 'A173', 'A173', 'A172', 'A174', 'A172', 'A173', 'A174', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A174', 'A172', 'A174', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A174', 'A173', 'A172', 'A174', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A172', 'A173', 'A173', 'A173', 'A171', 'A173', 'A174', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A174', 'A173', 'A174', 'A172', 'A174', 'A173', 'A173', 'A174', 'A173', 'A173', 'A174', 'A173', 'A172', 'A172', 'A173', 'A172', 'A172', 'A173', 'A173', 'A173', 'A173', 'A172', 'A172', 'A173', 'A173', 'A171', 'A174', 'A172', 'A174', 'A172', 'A173', 'A172', 'A173', 'A173', 'A174', 'A172', 'A173', 'A172', 'A173', 'A173', 'A173', 'A172', 'A172', 'A173', 'A173', 'A173', 'A171', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A174', 'A173', 'A172', 'A172', 'A173', 'A173', 'A172', 'A173', 'A174', 'A174', 'A173', 'A171', 'A173', 'A173', 'A173', 'A174', 'A173', 'A173', 'A173', 'A172', 'A172', 'A174', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A174', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A171', 'A173', 'A172', 'A173', 'A173', 'A171', 'A173', 'A172', 'A173', 'A174', 'A171', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A174', 'A173', 'A173', 'A173', 'A173', 'A172', 'A174', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A174', 'A173', 'A174', 'A173', 'A174', 'A173', 'A173', 'A171', 'A173', 'A173', 'A173', 'A173', 'A174', 'A173', 'A174', 'A173', 'A172', 'A173', 'A173', 'A174', 'A174', 'A173', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A174', 'A174', 'A172', 'A173', 'A172', 'A172', 'A173', 'A172', 'A173', 'A173', 'A173', 'A174', 'A173', 'A174', 'A173', 'A174', 'A173', 'A174', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A172', 'A173', 'A173', 'A173', 'A173', 'A174', 'A172', 'A174', 'A173', 'A174', 'A174', 'A172', 'A173', 'A172', 'A173', 'A173', 'A173', 'A174', 'A172', 'A173', 'A171', 'A173', 'A173', 'A174', 'A173', 'A174', 'A172', 'A172', 'A172', 'A173', 'A173', 'A173', 'A173', 'A173', 'A172', 'A173', 'A172', 'A174', 'A173', 'A172', 'A173', 'A172', 'A171', 'A173', 'A173', 'A172', 'A173', 'A173', 'A172', 'A173', 'A173', 'A174', 'A174', 'A173', 'A172', 'A173', 'A173', 'A173', 'A174', 'A172', 'A172', 'A172', 'A172', 'A174', 'A173', 'A172', 'A174', 'A173', 'A173', 'A173']\n"]}], "source": ["from sklearn.preprocessing import LabelEncoder\n", "le = LabelEncoder()\n", "le.fit(Job)\n", "print(list(le.classes_))\n", "encoded_labels = le.transform(Job)\n", "print(encoded_labels.tolist())\n", "decoded_labels = list(le.inverse_transform(encoded_labels))\n", "print(decoded_labels)"]}, {"cell_type": "code", "execution_count": 7, "id": "cbbbe9cf-e02f-4ace-a4d1-994e50694b43", "metadata": {}, "outputs": [], "source": ["from category_encoders import WOEEncoder\n", "encoder = WOEEncoder()\n", "WOE_Job_train= encoder.fit(X_train['Job'],y_train)\n", "WOE_Job_test = encoder.transform(X_test['Job'])"]}, {"cell_type": "code", "execution_count": 8, "id": "8009db8d-d46b-4e63-8359-aaf4cbf7f481", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          Job\n", "808  0.138963\n", "83  -0.195166\n", "643  0.026474\n", "895  0.138963\n", "536  0.138963\n", "..        ...\n", "994  0.026474\n", "824  0.138963\n", "993  0.138963\n", "825  0.026474\n", "945  0.026474\n", "\n", "[250 rows x 1 columns]\n"]}], "source": ["print(WOE_Job_test)"]}, {"cell_type": "code", "execution_count": 9, "id": "9d754494-3a2c-465f-80cc-403a983667c1", "metadata": {}, "outputs": [], "source": ["## 负WOE：说明该分箱的违约风险高于整体平均水平。\n", "## 正WOE：说明该分箱的违约风险低于整体平均水平。"]}, {"cell_type": "code", "execution_count": 10, "id": "c58699f8-9f28-4bbc-a7c4-11fa01290d01", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[INFO] creating woe binning ...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["D:\\anancoda\\Lib\\site-packages\\scorecardpy\\condition_fun.py:141: UserWarning: The positive value in \"creditability\" was replaced by 1 and negative value by 0.\n", "  warnings.warn(\"The positive value in \\\"{}\\\" was replaced by 1 and negative value by 0.\".format(y))\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\condition_fun.py:40: FutureWarning: errors='ignore' is deprecated and will raise in a future version. Use to_numeric without passing `errors` and catch exceptions explicitly instead\n", "  datetime_cols = dat.apply(pd.to_numeric,errors='ignore').select_dtypes(object).apply(pd.to_datetime,errors='ignore').select_dtypes('datetime64').columns.tolist()\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\condition_fun.py:40: FutureWarning: errors='ignore' is deprecated and will raise in a future version. Use to_datetime without passing `errors` and catch exceptions explicitly instead\n", "  datetime_cols = dat.apply(pd.to_numeric,errors='ignore').select_dtypes(object).apply(pd.to_datetime,errors='ignore').select_dtypes('datetime64').columns.tolist()\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\condition_fun.py:40: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  datetime_cols = dat.apply(pd.to_numeric,errors='ignore').select_dtypes(object).apply(pd.to_datetime,errors='ignore').select_dtypes('datetime64').columns.tolist()\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:332: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  init_bin = dtm.groupby('value', group_keys=False)['y'].agg([n0,n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:320: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  init_bin = dtm.groupby('bin', group_keys=False)['y'].agg([n0, n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:413: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  .groupby(['variable', 'bstbin', 'value'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:320: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  init_bin = dtm.groupby('bin', group_keys=False)['y'].agg([n0, n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:413: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  .groupby(['variable', 'bstbin', 'value'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:320: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  init_bin = dtm.groupby('bin', group_keys=False)['y'].agg([n0, n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:160: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise in a future error of pandas. Value '['[6.0,8.0)', '[48.0,50.0)', '[12.0,14.0)', '[42.0,44.0)', '[24.0,26.0)', ..., '[12.0,14.0)', '[30.0,32.0)', '[12.0,14.0)', '[44.0,46.0)', '[44.0,46.0)']\n", "Length: 1000\n", "Categories (24, object): ['[-inf,6.0)' < '[6.0,8.0)' < '[8.0,10.0)' < '[10.0,12.0)' ... '[46.0,48.0)' < '[48.0,50.0)' < '[50.0,56.0)' < '[56.0,inf)']' has dtype incompatible with category, please explicitly cast to a compatible dtype first.\n", "  dtm.loc[:,'bin'] = pd.cut(dtm['value'], bstbrks, right=False, labels=labels)\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:161: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning = dtm.groupby(['variable','bin'], group_keys=False)['y'].agg([n0, n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:361: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  init_bin = init_bin.groupby('brkp', group_keys=False).agg({\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:361: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  init_bin = init_bin.groupby('brkp', group_keys=False).agg({\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:361: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  init_bin = init_bin.groupby('brkp', group_keys=False).agg({\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:361: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  init_bin = init_bin.groupby('brkp', group_keys=False).agg({\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:332: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  init_bin = dtm.groupby('value', group_keys=False)['y'].agg([n0,n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:332: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  init_bin = dtm.groupby('value', group_keys=False)['y'].agg([n0,n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:320: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  init_bin = dtm.groupby('bin', group_keys=False)['y'].agg([n0, n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:361: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  init_bin = init_bin.groupby('brkp', group_keys=False).agg({\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:361: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  init_bin = init_bin.groupby('brkp', group_keys=False).agg({\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:361: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  init_bin = init_bin.groupby('brkp', group_keys=False).agg({\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:361: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  init_bin = init_bin.groupby('brkp', group_keys=False).agg({\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:361: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  init_bin = init_bin.groupby('brkp', group_keys=False).agg({\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:361: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  init_bin = init_bin.groupby('brkp', group_keys=False).agg({\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:361: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  init_bin = init_bin.groupby('brkp', group_keys=False).agg({\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:361: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  init_bin = init_bin.groupby('brkp', group_keys=False).agg({\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:332: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  init_bin = dtm.groupby('value', group_keys=False)['y'].agg([n0,n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:332: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  init_bin = dtm.groupby('value', group_keys=False)['y'].agg([n0,n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:361: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  init_bin = init_bin.groupby('brkp', group_keys=False).agg({\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:320: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  init_bin = dtm.groupby('bin', group_keys=False)['y'].agg([n0, n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:332: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  init_bin = dtm.groupby('value', group_keys=False)['y'].agg([n0,n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:332: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  init_bin = dtm.groupby('value', group_keys=False)['y'].agg([n0,n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:320: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  init_bin = dtm.groupby('bin', group_keys=False)['y'].agg([n0, n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:160: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise in a future error of pandas. Value '['[67.0,68.0)', '[22.0,23.0)', '[49.0,50.0)', '[45.0,46.0)', '[53.0,54.0)', ..., '[31.0,32.0)', '[40.0,41.0)', '[38.0,39.0)', '[23.0,24.0)', '[27.0,28.0)']\n", "Length: 1000\n", "Categories (53, object): ['[-inf,20.0)' < '[20.0,21.0)' < '[21.0,22.0)' < '[22.0,23.0)' ... '[68.0,69.0)' < '[69.0,71.0)' < '[71.0,75.0)' < '[75.0,inf)']' has dtype incompatible with category, please explicitly cast to a compatible dtype first.\n", "  dtm.loc[:,'bin'] = pd.cut(dtm['value'], bstbrks, right=False, labels=labels)\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:161: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning = dtm.groupby(['variable','bin'], group_keys=False)['y'].agg([n0, n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:361: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  init_bin = init_bin.groupby('brkp', group_keys=False).agg({\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:361: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  init_bin = init_bin.groupby('brkp', group_keys=False).agg({\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:361: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  init_bin = init_bin.groupby('brkp', group_keys=False).agg({\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:361: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  init_bin = init_bin.groupby('brkp', group_keys=False).agg({\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:361: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  init_bin = init_bin.groupby('brkp', group_keys=False).agg({\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:361: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  init_bin = init_bin.groupby('brkp', group_keys=False).agg({\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:332: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  init_bin = dtm.groupby('value', group_keys=False)['y'].agg([n0,n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:332: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  init_bin = dtm.groupby('value', group_keys=False)['y'].agg([n0,n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:332: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  init_bin = dtm.groupby('value', group_keys=False)['y'].agg([n0,n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:332: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  init_bin = dtm.groupby('value', group_keys=False)['y'].agg([n0,n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:320: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  init_bin = dtm.groupby('bin', group_keys=False)['y'].agg([n0, n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:442: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:443: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index().assign(bin=lambda x: x['bstbin'])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:332: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  init_bin = dtm.groupby('value', group_keys=False)['y'].agg([n0,n1])\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:414: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:446: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  binning_1bst_brk = binning_1bst_brk.groupby(['variable', 'bstbin'], group_keys=False)\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n", "D:\\anancoda\\Lib\\site-packages\\scorecardpy\\woebin.py:447: FutureWarning: The provided callable <built-in function sum> is currently using SeriesGroupBy.sum. In a future version of pandas, the provided callable will be used directly. To keep current behavior pass the string \"sum\" instead.\n", "  .agg({'good':sum, 'bad':sum, 'bin':lambda x:'%,%'.join(x)}).reset_index()\\\n"]}, {"data": {"text/plain": ["{'property':    variable                                                bin  count  \\\n", " 0  property                                        real estate    282   \n", " 1  property  building society savings agreement/ life insur...    232   \n", " 2  property  car or other, not in attribute Savings account...    332   \n", " 3  property                              unknown / no property    154   \n", " \n", "    count_distr  good  bad   badprob       woe    bin_iv  total_iv  \\\n", " 0        0.282   222   60  0.212766 -0.461035  0.054007  0.112638   \n", " 1        0.232   161   71  0.306034  0.028573  0.000190  0.112638   \n", " 2        0.332   230  102  0.307229  0.034191  0.000391  0.112638   \n", " 3        0.154    87   67  0.435065  0.586082  0.058050  0.112638   \n", " \n", "                                               breaks  is_special_values  \n", " 0                                        real estate              False  \n", " 1  building society savings agreement/ life insur...              False  \n", " 2  car or other, not in attribute Savings account...              False  \n", " 3                              unknown / no property              False  ,\n", " 'present_residence_since':                   variable         bin  count  count_distr  good  bad  \\\n", " 0  present_residence_since  [-inf,2.0)    130        0.130    94   36   \n", " 1  present_residence_since   [2.0,3.0)    308        0.308   211   97   \n", " 2  present_residence_since   [3.0,4.0)    149        0.149   106   43   \n", " 3  present_residence_since   [4.0,inf)    413        0.413   289  124   \n", " \n", "     badprob       woe        bin_iv  total_iv breaks  is_special_values  \n", " 0  0.276923 -0.112478  1.606828e-03  0.003589    2.0              False  \n", " 1  0.314935  0.070151  1.536634e-03  0.003589    3.0              False  \n", " 2  0.288591 -0.054941  4.447614e-04  0.003589    4.0              False  \n", " 3  0.300242  0.001153  5.489228e-07  0.003589    inf              False  ,\n", " 'number_of_people_being_liable_to_provide_maintenance_for':                                             variable         bin  count  \\\n", " 0  number_of_people_being_liable_to_provide_maint...  [-inf,2.0)    845   \n", " 1  number_of_people_being_liable_to_provide_maint...   [2.0,inf)    155   \n", " \n", "    count_distr  good  bad   badprob       woe    bin_iv  total_iv breaks  \\\n", " 0        0.845   591  254  0.300592  0.002816  0.000007  0.000043    2.0   \n", " 1        0.155   109   46  0.296774 -0.015409  0.000037  0.000043    inf   \n", " \n", "    is_special_values  \n", " 0              False  \n", " 1              False  ,\n", " 'duration_in_month':             variable          bin  count  count_distr  good  bad   badprob  \\\n", " 0  duration_in_month   [-inf,8.0)     87        0.087    78    9  0.103448   \n", " 1  duration_in_month   [8.0,16.0)    344        0.344   264   80  0.232558   \n", " 2  duration_in_month  [16.0,34.0)    399        0.399   270  129  0.323308   \n", " 3  duration_in_month  [34.0,44.0)    100        0.100    58   42  0.420000   \n", " 4  duration_in_month   [44.0,inf)     70        0.070    30   40  0.571429   \n", " \n", "         woe    bin_iv  total_iv breaks  is_special_values  \n", " 0 -1.312186  0.106849  0.282618    8.0              False  \n", " 1 -0.346625  0.038294  0.282618   16.0              False  \n", " 2  0.108688  0.004813  0.282618   34.0              False  \n", " 3  0.524524  0.029973  0.282618   44.0              False  \n", " 4  1.134980  0.102689  0.282618    inf              False  ,\n", " 'other_debtors_or_guarantors':                       variable                  bin  count  count_distr  good  \\\n", " 0  other_debtors_or_guarantors  none%,%co-applicant    948        0.948   658   \n", " 1  other_debtors_or_guarantors            guarantor     52        0.052    42   \n", " \n", "    bad   badprob       woe    bin_iv  total_iv               breaks  \\\n", " 0  290  0.305907  0.027974  0.000746   0.01642  none%,%co-applicant   \n", " 1   10  0.192308 -0.587787  0.015674   0.01642            guarantor   \n", " \n", "    is_special_values  \n", " 0              False  \n", " 1              False  ,\n", " 'telephone':     variable                                       bin  count  count_distr  \\\n", " 0  telephone                                      none    596        0.596   \n", " 1  telephone  yes, registered under the customers name    404        0.404   \n", " \n", "    good  bad   badprob       woe    bin_iv  total_iv  \\\n", " 0   409  187  0.313758  0.064691  0.002526  0.006378   \n", " 1   291  113  0.279703 -0.098638  0.003852  0.006378   \n", " \n", "                                      breaks  is_special_values  \n", " 0                                      none              False  \n", " 1  yes, registered under the customers name              False  ,\n", " 'credit_amount':         variable              bin  count  count_distr  good  bad   badprob  \\\n", " 0  credit_amount    [-inf,1400.0)    267        0.267   185   82  0.307116   \n", " 1  credit_amount  [1400.0,1800.0)    105        0.105    87   18  0.171429   \n", " 2  credit_amount  [1800.0,4000.0)    382        0.382   287   95  0.248691   \n", " 3  credit_amount  [4000.0,9200.0)    196        0.196   120   76  0.387755   \n", " 4  credit_amount     [9200.0,inf)     50        0.050    21   29  0.580000   \n", " \n", "         woe    bin_iv  total_iv  breaks  is_special_values  \n", " 0  0.033661  0.000305   0.18122  1400.0              False  \n", " 1 -0.728239  0.046815   0.18122  1800.0              False  \n", " 2 -0.258307  0.024109   0.18122  4000.0              False  \n", " 3  0.390539  0.031987   0.18122  9200.0              False  \n", " 4  1.170071  0.078005   0.18122     inf              False  ,\n", " 'purpose':   variable                                                bin  count  \\\n", " 0  purpose                            retraining%,%car (used)    112   \n", " 1  purpose                                   radio/television    280   \n", " 2  purpose  furniture/equipment%,%domestic appliances%,%bu...    608   \n", " \n", "    count_distr  good  bad   badprob       woe    bin_iv  total_iv  \\\n", " 0        0.112    94   18  0.160714 -0.805625  0.059846  0.152924   \n", " 1        0.280   218   62  0.221429 -0.410063  0.042959  0.152924   \n", " 2        0.608   388  220  0.361842  0.279920  0.050119  0.152924   \n", " \n", "                                               breaks  is_special_values  \n", " 0                            retraining%,%car (used)              False  \n", " 1                                   radio/television              False  \n", " 2  furniture/equipment%,%domestic appliances%,%bu...              False  ,\n", " 'savings_account_and_bonds':                     variable  \\\n", " 0  savings_account_and_bonds   \n", " 1  savings_account_and_bonds   \n", " 2  savings_account_and_bonds   \n", " \n", "                                                  bin  count  count_distr  \\\n", " 0                                       ... < 100 DM    603        0.603   \n", " 1                                100 <= ... < 500 DM    103        0.103   \n", " 2  500 <= ... < 1000 DM%,%... >= 1000 DM%,%unknow...    294        0.294   \n", " \n", "    good  bad   badprob       woe    bin_iv  total_iv  \\\n", " 0   386  217  0.359867  0.271358  0.046648  0.190974   \n", " 1    69   34  0.330097  0.139552  0.002060  0.190974   \n", " 2   245   49  0.166667 -0.762140  0.142266  0.190974   \n", " \n", "                                               breaks  is_special_values  \n", " 0                                       ... < 100 DM              False  \n", " 1                                100 <= ... < 500 DM              False  \n", " 2  500 <= ... < 1000 DM%,%... >= 1000 DM%,%unknow...              False  ,\n", " 'personal_status_and_sex':                   variable                                       bin  count  \\\n", " 0  personal_status_and_sex                 male : divorced/separated     50   \n", " 1  personal_status_and_sex       female : divorced/separated/married    310   \n", " 2  personal_status_and_sex                             male : single    548   \n", " 3  personal_status_and_sex  male : married/widowed%,%female : single     92   \n", " \n", "    count_distr  good  bad   badprob       woe    bin_iv  total_iv  \\\n", " 0        0.050    38   12  0.240000 -0.305382  0.004363   0.00884   \n", " 1        0.310   221   89  0.287097 -0.062228  0.001185   0.00884   \n", " 2        0.548   380  168  0.306569  0.031091  0.000533   0.00884   \n", " 3        0.092    61   31  0.336957  0.170411  0.002759   0.00884   \n", " \n", "                                      breaks  is_special_values  \n", " 0                 male : divorced/separated              False  \n", " 1       female : divorced/separated/married              <PERSON><PERSON><PERSON>  \n", " 2                             male : single              False  \n", " 3  male : married/widowed%,%female : single              False  ,\n", " 'installment_rate_in_percentage_of_disposable_income':                                             variable         bin  count  \\\n", " 0  installment_rate_in_percentage_of_disposable_i...  [-inf,3.0)    367   \n", " 1  installment_rate_in_percentage_of_disposable_i...   [3.0,4.0)    157   \n", " 2  installment_rate_in_percentage_of_disposable_i...   [4.0,inf)    476   \n", " \n", "    count_distr  good  bad   badprob       woe    bin_iv  total_iv breaks  \\\n", " 0        0.367   271   96  0.261580 -0.190473  0.012789  0.025569    3.0   \n", " 1        0.157   112   45  0.286624 -0.064539  0.000645  0.025569    4.0   \n", " 2        0.476   317  159  0.334034  0.157300  0.012135  0.025569    inf   \n", " \n", "    is_special_values  \n", " 0              False  \n", " 1              False  \n", " 2              False  ,\n", " 'foreign_worker':          variable       bin  count  count_distr  good  bad  badprob  woe  \\\n", " 0  foreign_worker  yes%,%no   1000          1.0   700  300      0.3  0.0   \n", " \n", "    bin_iv  total_iv    breaks  is_special_values  \n", " 0     0.0       0.0  yes%,%no              False  ,\n", " 'credit_history':          variable                                                bin  count  \\\n", " 0  credit_history  no credits taken/ all credits paid back duly%,...     89   \n", " 1  credit_history           existing credits paid back duly till now    530   \n", " 2  credit_history                    delay in paying off in the past     88   \n", " 3  credit_history  critical account/ other credits existing (not ...    293   \n", " \n", "    count_distr  good  bad   badprob       woe    bin_iv  total_iv  \\\n", " 0        0.089    36   53  0.595506  1.234071  0.154553   0.29183   \n", " 1        0.530   361  169  0.318868  0.088319  0.004206   0.29183   \n", " 2        0.088    60   28  0.318182  0.085158  0.000649   0.29183   \n", " 3        0.293   243   50  0.170648 -0.733741  0.132423   0.29183   \n", " \n", "                                               breaks  is_special_values  \n", " 0  no credits taken/ all credits paid back duly%,...              False  \n", " 1           existing credits paid back duly till now              False  \n", " 2                    delay in paying off in the past              False  \n", " 3  critical account/ other credits existing (not ...              False  ,\n", " 'age_in_years':        variable          bin  count  count_distr  good  bad   badprob  \\\n", " 0  age_in_years  [-inf,26.0)    190        0.190   110   80  0.421053   \n", " 1  age_in_years  [26.0,28.0)    101        0.101    74   27  0.267327   \n", " 2  age_in_years  [28.0,35.0)    257        0.257   172   85  0.330739   \n", " 3  age_in_years  [35.0,37.0)     79        0.079    67   12  0.151899   \n", " 4  age_in_years   [37.0,inf)    373        0.373   277   96  0.257373   \n", " \n", "         woe    bin_iv  total_iv breaks  is_special_values  \n", " 0  0.528844  0.057921  0.130499   26.0              False  \n", " 1 -0.160930  0.002529  0.130499   28.0              False  \n", " 2  0.142455  0.005359  0.130499   35.0              False  \n", " 3 -0.872488  0.048610  0.130499   37.0              False  \n", " 4 -0.212371  0.016080  0.130499    inf              False  ,\n", " 'housing':   variable       bin  count  count_distr  good  bad   badprob       woe  \\\n", " 0  housing      rent    179        0.179   109   70  0.391061  0.404445   \n", " 1  housing       own    713        0.713   527  186  0.260870 -0.194156   \n", " 2  housing  for free    108        0.108    64   44  0.407407  0.472604   \n", " \n", "      bin_iv  total_iv    breaks  is_special_values  \n", " 0  0.031393  0.083293      rent              False  \n", " 1  0.025795  0.083293       own              False  \n", " 2  0.026106  0.083293  for free              False  ,\n", " 'other_installment_plans':                   variable            bin  count  count_distr  good  bad  \\\n", " 0  other_installment_plans  bank%,%stores    186        0.186   110   76   \n", " 1  other_installment_plans           none    814        0.814   590  224   \n", " \n", "     badprob       woe    bin_iv  total_iv         breaks  is_special_values  \n", " 0  0.408602  0.477551  0.045936  0.057592  bank%,%stores              False  \n", " 1  0.275184 -0.121179  0.011656  0.057592           none              False  ,\n", " 'status_of_existing_checking_account':                               variable  \\\n", " 0  status_of_existing_checking_account   \n", " 1  status_of_existing_checking_account   \n", " 2  status_of_existing_checking_account   \n", " \n", "                                                  bin  count  count_distr  \\\n", " 0                     ... < 0 DM%,%0 <= ... < 200 DM    543        0.543   \n", " 1  ... >= 200 DM / salary assignments for at leas...     63        0.063   \n", " 2                                no checking account    394        0.394   \n", " \n", "    good  bad   badprob       woe    bin_iv  total_iv  \\\n", " 0   303  240  0.441989  0.614204  0.225501  0.639372   \n", " 1    49   14  0.222222 -0.405465  0.009461  0.639372   \n", " 2   348   46  0.116751 -1.176263  0.404410  0.639372   \n", " \n", "                                               breaks  is_special_values  \n", " 0                     ... < 0 DM%,%0 <= ... < 200 DM              False  \n", " 1  ... >= 200 DM / salary assignments for at leas...              False  \n", " 2                                no checking account              False  ,\n", " 'job':   variable                                                bin  count  \\\n", " 0      job  unemployed/ unskilled - non-resident%,%unskill...    222   \n", " 1      job                        skilled employee / official    630   \n", " 2      job  management/ self-employed/ highly qualified em...    148   \n", " \n", "    count_distr  good  bad   badprob       woe    bin_iv  total_iv  \\\n", " 0        0.222   159   63  0.283784 -0.078472  0.001345  0.008095   \n", " 1        0.630   444  186  0.295238 -0.022780  0.000325  0.008095   \n", " 2        0.148    97   51  0.344595  0.204413  0.006424  0.008095   \n", " \n", "                                               breaks  is_special_values  \n", " 0  unemployed/ unskilled - non-resident%,%unskill...              False  \n", " 1                        skilled employee / official              False  \n", " 2  management/ self-employed/ highly qualified em...              False  ,\n", " 'number_of_existing_credits_at_this_bank':                                   variable         bin  count  count_distr  \\\n", " 0  number_of_existing_credits_at_this_bank  [-inf,2.0)    633        0.633   \n", " 1  number_of_existing_credits_at_this_bank   [2.0,inf)    367        0.367   \n", " \n", "    good  bad   badprob       woe    bin_iv  total_iv breaks  is_special_values  \n", " 0   433  200  0.315956  0.074877  0.003601  0.010084    2.0              False  \n", " 1   267  100  0.272480 -0.134781  0.006482  0.010084    inf              False  ,\n", " 'present_employment_since':                    variable                        bin  count  count_distr  \\\n", " 0  present_employment_since  unemployed%,%... < 1 year    234        0.234   \n", " 1  present_employment_since         1 <= ... < 4 years    339        0.339   \n", " 2  present_employment_since         4 <= ... < 7 years    174        0.174   \n", " 3  present_employment_since             ... >= 7 years    253        0.253   \n", " \n", "    good  bad   badprob       woe    bin_iv  total_iv  \\\n", " 0   141   93  0.397436  0.431137  0.046809  0.085301   \n", " 1   235  104  0.306785  0.032103  0.000352  0.085301   \n", " 2   135   39  0.224138 -0.394415  0.024792  0.085301   \n", " 3   189   64  0.252964 -0.235566  0.013349  0.085301   \n", " \n", "                       breaks  is_special_values  \n", " 0  unemployed%,%... < 1 year              False  \n", " 1         1 <= ... < 4 years              False  \n", " 2         4 <= ... < 7 years              False  \n", " 3             ... >= 7 years              False  }"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["import scorecardpy as sc\n", "\n", "# 加载示例数据\n", "df = sc.germancredit()\n", "\n", "# 使用 woebin 进行分箱\n", "bins = sc.woebin(df, y='creditability')\n", "\n", "bins"]}, {"cell_type": "code", "execution_count": 11, "id": "05ad2145-8ac8-4b6d-95b1-b2478a940c51", "metadata": {}, "outputs": [], "source": ["### 卡方最优分箱"]}, {"cell_type": "code", "execution_count": 12, "id": "6e00a07f-4d82-4795-a03b-1121b934f9fe", "metadata": {}, "outputs": [], "source": ["### toad 包"]}, {"cell_type": "code", "execution_count": 16, "id": "8f47a9b2-325f-4d9b-a4e8-218725920bae", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([26, 35, 37, 49], dtype=int64)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["## 单列分箱\n", "import toad\n", "Age_K = toad.ChiMerge(df['Age'],df['class'],n_bins = 5)\n", "Age_K"]}, {"cell_type": "code", "execution_count": 17, "id": "72195821-da64-4f99-bfa8-f834ec78668d", "metadata": {}, "outputs": [], "source": ["## 全表分箱\n", "combiner = toad.transform.Co<PERSON>iner()\n", "combiner.fit(df, y='class', method='chi', min_samples=0.05)\n", "bins = combiner.export()           # 字典形式保存各列切点\n", "data_bin = combiner.transform(df) "]}, {"cell_type": "code", "execution_count": 18, "id": "a1770355-f234-4b7c-b415-5a891e16b7c5", "metadata": {}, "outputs": [], "source": ["## toad 默认使用显著性水平 0.05，可通过 alpha=0.1 调整阈值"]}, {"cell_type": "code", "execution_count": 33, "id": "985fb3e9-b3ab-49d8-acfc-de442108cbfd", "metadata": {}, "outputs": [], "source": ["### 最优iv分箱"]}, {"cell_type": "code", "execution_count": 22, "id": "22ac1109-44b6-4c58-8123-9cd3c3244ce4", "metadata": {}, "outputs": [], "source": ["### 利用决策树确定最优分箱边界：通过DecisionTreeClassifier的tree_属性获取决策树的节点划分值，作为分箱的边界。\n", "### 基于分箱边界进行分箱：使用pandas.cut函数根据边界对数据进行分箱。\n", "### 计算每个分箱的WOE和IV值：根据分箱结果计算每个分箱的WOE值和IV值。"]}, {"cell_type": "code", "execution_count": 4, "id": "7de241c0-d234-4272-8e7c-da93331a12a8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["该变量IV = 0.*****************\n", "              good  bad  total  good_pct   bad_pct  total_pct  bad_rate  \\\n", "bins                                                                      \n", "[19.0, 25.5)   110   80    190  0.157143  0.266667      0.190  0.421053   \n", "[25.5, 27.5)    74   27    101  0.105714  0.090000      0.101  0.267327   \n", "[27.5, 34.5)   172   85    257  0.245714  0.283333      0.257  0.330739   \n", "[34.5, 36.5)    67   12     79  0.095714  0.040000      0.079  0.151899   \n", "[36.5, 52.5)   210   67    277  0.300000  0.223333      0.277  0.241877   \n", "[52.5, 75.1)    67   29     96  0.095714  0.096667      0.096  0.302083   \n", "\n", "                   woe        iv  \n", "bins                              \n", "[19.0, 25.5) -0.528844  0.057921  \n", "[25.5, 27.5)  0.160930  0.002529  \n", "[27.5, 34.5) -0.142455  0.005359  \n", "[34.5, 36.5)  0.872488  0.048610  \n", "[36.5, 52.5)  0.295117  0.022626  \n", "[52.5, 75.1) -0.009901  0.000009  \n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\959822559.py:42: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n"]}], "source": ["from sklearn.tree import DecisionTreeClassifier\n", "import numpy as np\n", "def optimal_binning_boundary(x: pd.Series, y: pd.Series, nan: float = -999.) -> list:\n", "    \"\"\"\n", "    利用决策树获得最优分箱的边界值列表\n", "    \"\"\"\n", "    boundary = []  # 分箱边界值列表\n", "    x = x.fillna(nan).values  # 填充缺失值\n", "    y = y.values\n", "\n", "    clf = DecisionTreeClassifier(criterion='entropy',  # 使用信息熵作为划分准则\n", "                                 max_leaf_nodes=6,     # 最大叶子节点数\n", "                                 min_samples_leaf=0.05)  # 叶子节点样本数量最小占比\n", "    clf.fit(x.reshape(-1, 1), y)  # 训练决策树\n", "\n", "    n_nodes = clf.tree_.node_count\n", "    children_left = clf.tree_.children_left\n", "    children_right = clf.tree_.children_right\n", "    threshold = clf.tree_.threshold\n", "\n", "    for i in range(n_nodes):\n", "        if children_left[i] != children_right[i]:  # 获得决策树节点上的划分边界值\n", "            boundary.append(threshold[i])\n", "\n", "    boundary.sort()\n", "    min_x = x.min()\n", "    max_x = x.max() + 0.1  # +0.1是为了包含特征最大值的样本\n", "    boundary = [min_x] + boundary + [max_x]\n", "    return boundary\n", "\n", "# 定义函数：计算WOE和IV值\n", "def feature_woe_iv(x: pd.Series, y: pd.Series, nan: float = -999.) -> pd.DataFrame:\n", "    \"\"\"\n", "    计算变量各个分箱的WOE、IV值，返回一个DataFrame\n", "    \"\"\"\n", "    x = x.fillna(nan)\n", "    boundary = optimal_binning_boundary(x, y, nan)  # 获得最优分箱边界值列表\n", "    df = pd.concat([x, y], axis=1)  # 合并x、y为一个DataFrame\n", "    df.columns = ['x', 'y']  # 重命名字段\n", "    df['bins'] = pd.cut(x=x, bins=boundary, right=False)  # 获得每个x值所在的分箱区间\n", "\n", "    grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "    result_df = grouped.agg([('good', lambda y: (y == 0).sum()),\n", "                             ('bad', lambda y: (y == 1).sum()),\n", "                             ('total', 'count')])\n", "\n", "    result_df['good_pct'] = result_df['good'] / result_df['good'].sum()  # 好客户占比\n", "    result_df['bad_pct'] = result_df['bad'] / result_df['bad'].sum()  # 坏客户占比\n", "    result_df['total_pct'] = result_df['total'] / result_df['total'].sum()  # 总客户占比\n", "\n", "    result_df['bad_rate'] = result_df['bad'] / result_df['total']  # 坏比率\n", "    result_df['woe'] = np.log(result_df['good_pct'] / result_df['bad_pct'])  # WOE\n", "    result_df['iv'] = (result_df['good_pct'] - result_df['bad_pct']) * result_df['woe']  # IV\n", "\n", "    print(f\"该变量IV = {result_df['iv'].sum()}\")  # 输出总IV值\n", "    return result_df\n", "result = feature_woe_iv(df['Age'], df['class'])\n", "print(result)"]}, {"cell_type": "code", "execution_count": 5, "id": "c6293a40-476e-4777-9257-1b27940a6d91", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["分箱边界： [18.9 25.5 27.5 34.5 36.5 52.5 75.1]\n", "  Status_of_existing_checking_account  Duration Credit_history Purpose  \\\n", "0                                 A11         6            A34     A43   \n", "1                                 A12        48            A32     A43   \n", "2                                 A14        12            A34     A46   \n", "3                                 A11        42            A32     A42   \n", "4                                 A11        24            A33     A40   \n", "\n", "   Credit_amount Savings_account/bonds Present_employment_since  \\\n", "0           1169                   A65                      A75   \n", "1           5951                   A61                      A73   \n", "2           2096                   A61                      A74   \n", "3           7882                   A61                      A74   \n", "4           4870                   A61                      A73   \n", "\n", "   Installment_rate_in_percentage_of_disposable_income  \\\n", "0                                                  4     \n", "1                                                  2     \n", "2                                                  2     \n", "3                                                  2     \n", "4                                                  3     \n", "\n", "  Personal_status_and_sex Other_debtors/guarantors  ...  Age  \\\n", "0                     A93                     A101  ...   67   \n", "1                     A92                     A101  ...   22   \n", "2                     A93                     A101  ...   49   \n", "3                     A93                     A103  ...   45   \n", "4                     A93                     A101  ...   53   \n", "\n", "  Other_installment_plans  Housing Number_of_existing_credits_at_this_bank  \\\n", "0                    A143     A152                                       2   \n", "1                    A143     A152                                       1   \n", "2                    A143     A152                                       1   \n", "3                    A143     A153                                       1   \n", "4                    A143     A153                                       2   \n", "\n", "    Job  Number_of_people_being_liable_to_provide_maintenance_for Telephone  \\\n", "0  A173                                                  1             A192   \n", "1  A173                                                  1             A191   \n", "2  A172                                                  2             A191   \n", "3  A173                                                  2             A191   \n", "4  A173                                                  2             A191   \n", "\n", "   foreign_worker class bin  \n", "0            A201     0   5  \n", "1            A201     1   0  \n", "2            A201     0   4  \n", "3            A201     0   4  \n", "4            A201     1   5  \n", "\n", "[5 rows x 22 columns]\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from sklearn.tree import DecisionTreeRegressor\n", "\n", "# 示例数据\n", "\n", "\n", "# 使用DecisionTreeRegressor进行分箱\n", "clf = DecisionTreeRegressor(max_leaf_nodes=6, random_state=42)\n", "clf.fit(df[['Age']], df['class'])\n", "\n", "# 获取分箱边界\n", "cut_points = clf.tree_.threshold[clf.tree_.feature != -2]\n", "cut_points = np.append(cut_points, [df['Age'].min() - 0.1, df['Age'].max() + 0.1])\n", "cut_points = np.unique(cut_points)  # 去重并排序\n", "cut_points.sort()\n", "\n", "# 对数据进行分箱\n", "df['bin'] = pd.cut(df['Age'], bins=cut_points, labels=False)\n", "\n", "print(\"分箱边界：\", cut_points)\n", "print(df.head())"]}, {"cell_type": "code", "execution_count": 6, "id": "53ac7f43-00a7-4da9-9a41-f08472df2ca8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    Status_of_existing_checking_account  Duration Credit_history Purpose  \\\n", "0                                   A11         6            A34     A43   \n", "1                                   A12        48            A32     A43   \n", "2                                   A14        12            A34     A46   \n", "3                                   A11        42            A32     A42   \n", "4                                   A11        24            A33     A40   \n", "..                                  ...       ...            ...     ...   \n", "995                                 A14        12            A32     A42   \n", "996                                 A11        30            A32     A41   \n", "997                                 A14        12            A32     A43   \n", "998                                 A11        45            A32     A43   \n", "999                                 A12        45            A34     A41   \n", "\n", "     Credit_amount Savings_account/bonds Present_employment_since  \\\n", "0             1169                   A65                      A75   \n", "1             5951                   A61                      A73   \n", "2             2096                   A61                      A74   \n", "3             7882                   A61                      A74   \n", "4             4870                   A61                      A73   \n", "..             ...                   ...                      ...   \n", "995           1736                   A61                      A74   \n", "996           3857                   A61                      A73   \n", "997            804                   A61                      A75   \n", "998           1845                   A61                      A73   \n", "999           4576                   A62                      A71   \n", "\n", "     Installment_rate_in_percentage_of_disposable_income  \\\n", "0                                                    4     \n", "1                                                    2     \n", "2                                                    2     \n", "3                                                    2     \n", "4                                                    3     \n", "..                                                 ...     \n", "995                                                  3     \n", "996                                                  4     \n", "997                                                  4     \n", "998                                                  4     \n", "999                                                  3     \n", "\n", "    Personal_status_and_sex Other_debtors/guarantors  ...  \\\n", "0                       A93                     A101  ...   \n", "1                       A92                     A101  ...   \n", "2                       A93                     A101  ...   \n", "3                       A93                     A103  ...   \n", "4                       A93                     A101  ...   \n", "..                      ...                      ...  ...   \n", "995                     A92                     A101  ...   \n", "996                     A91                     A101  ...   \n", "997                     A93                     A101  ...   \n", "998                     A93                     A101  ...   \n", "999                     A93                     A101  ...   \n", "\n", "     Other_installment_plans Housing  Number_of_existing_credits_at_this_bank  \\\n", "0                       A143    A152                                        2   \n", "1                       A143    A152                                        1   \n", "2                       A143    A152                                        1   \n", "3                       A143    A153                                        1   \n", "4                       A143    A153                                        2   \n", "..                       ...     ...                                      ...   \n", "995                     A143    A152                                        1   \n", "996                     A143    A152                                        1   \n", "997                     A143    A152                                        1   \n", "998                     A143    A153                                        1   \n", "999                     A143    A152                                        1   \n", "\n", "      Job Number_of_people_being_liable_to_provide_maintenance_for  Telephone  \\\n", "0    A173                                                  1             A192   \n", "1    A173                                                  1             A191   \n", "2    A172                                                  2             A191   \n", "3    A173                                                  2             A191   \n", "4    A173                                                  2             A191   \n", "..    ...                                                ...              ...   \n", "995  A172                                                  1             A191   \n", "996  A174                                                  1             A192   \n", "997  A173                                                  1             A191   \n", "998  A173                                                  1             A192   \n", "999  A173                                                  1             A191   \n", "\n", "    foreign_worker  class bin          bins  \n", "0             A201      0   5  [52.5, 75.1)  \n", "1             A201      1   0  [19.0, 25.5)  \n", "2             A201      0   4  [36.5, 52.5)  \n", "3             A201      0   4  [36.5, 52.5)  \n", "4             A201      1   5  [52.5, 75.1)  \n", "..             ...    ...  ..           ...  \n", "995           A201      0   2  [27.5, 34.5)  \n", "996           A201      0   4  [36.5, 52.5)  \n", "997           A201      0   4  [36.5, 52.5)  \n", "998           A201      1   0  [19.0, 25.5)  \n", "999           A201      0   1  [25.5, 27.5)  \n", "\n", "[1000 rows x 23 columns]\n"]}], "source": ["def binning_transform(df: pd.DataFrame, feature: str, target: str, nan: float = -999.) -> pd.DataFrame:\n", "    \"\"\"\n", "    根据分箱结果重新构建DataFrame，将特征值替换为分箱区间\n", "    \"\"\"\n", "    boundary = optimal_binning_boundary(df[feature], df[target], nan)  # 获取分箱边界\n", "    df['bins'] = pd.cut(x=df[feature], bins=boundary, right=False)  # 分箱\n", "    return df\n", "transformed_df = binning_transform(df, 'Age', 'class')\n", "print(transformed_df)"]}, {"cell_type": "code", "execution_count": 7, "id": "3347cd6a-d5ce-4f40-9a7a-0135348ec3c1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["该变量IV = 0.*****************\n", "              good  bad  total  good_pct   bad_pct  total_pct  bad_rate  \\\n", "bins                                                                      \n", "[19.0, 25.5)   110   80    190  0.157143  0.266667      0.190  0.421053   \n", "[25.5, 27.5)    74   27    101  0.105714  0.090000      0.101  0.267327   \n", "[27.5, 34.5)   172   85    257  0.245714  0.283333      0.257  0.330739   \n", "[34.5, 36.5)    67   12     79  0.095714  0.040000      0.079  0.151899   \n", "[36.5, 52.5)   210   67    277  0.300000  0.223333      0.277  0.241877   \n", "[52.5, 75.1)    67   29     96  0.095714  0.096667      0.096  0.302083   \n", "\n", "                   woe        iv  \n", "bins                              \n", "[19.0, 25.5) -0.528844  0.057921  \n", "[25.5, 27.5)  0.160930  0.002529  \n", "[27.5, 34.5) -0.142455  0.005359  \n", "[34.5, 36.5)  0.872488  0.048610  \n", "[36.5, 52.5)  0.295117  0.022626  \n", "[52.5, 75.1) -0.009901  0.000009  \n", "    Status_of_existing_checking_account  Duration Credit_history Purpose  \\\n", "0                                   A11         6            A34     A43   \n", "1                                   A12        48            A32     A43   \n", "2                                   A14        12            A34     A46   \n", "3                                   A11        42            A32     A42   \n", "4                                   A11        24            A33     A40   \n", "..                                  ...       ...            ...     ...   \n", "995                                 A14        12            A32     A42   \n", "996                                 A11        30            A32     A41   \n", "997                                 A14        12            A32     A43   \n", "998                                 A11        45            A32     A43   \n", "999                                 A12        45            A34     A41   \n", "\n", "     Credit_amount Savings_account/bonds Present_employment_since  \\\n", "0             1169                   A65                      A75   \n", "1             5951                   A61                      A73   \n", "2             2096                   A61                      A74   \n", "3             7882                   A61                      A74   \n", "4             4870                   A61                      A73   \n", "..             ...                   ...                      ...   \n", "995           1736                   A61                      A74   \n", "996           3857                   A61                      A73   \n", "997            804                   A61                      A75   \n", "998           1845                   A61                      A73   \n", "999           4576                   A62                      A71   \n", "\n", "     Installment_rate_in_percentage_of_disposable_income  \\\n", "0                                                    4     \n", "1                                                    2     \n", "2                                                    2     \n", "3                                                    2     \n", "4                                                    3     \n", "..                                                 ...     \n", "995                                                  3     \n", "996                                                  4     \n", "997                                                  4     \n", "998                                                  4     \n", "999                                                  3     \n", "\n", "    Personal_status_and_sex Other_debtors/guarantors  ...  \\\n", "0                       A93                     A101  ...   \n", "1                       A92                     A101  ...   \n", "2                       A93                     A101  ...   \n", "3                       A93                     A103  ...   \n", "4                       A93                     A101  ...   \n", "..                      ...                      ...  ...   \n", "995                     A92                     A101  ...   \n", "996                     A91                     A101  ...   \n", "997                     A93                     A101  ...   \n", "998                     A93                     A101  ...   \n", "999                     A93                     A101  ...   \n", "\n", "     Other_installment_plans Housing  Number_of_existing_credits_at_this_bank  \\\n", "0                       A143    A152                                        2   \n", "1                       A143    A152                                        1   \n", "2                       A143    A152                                        1   \n", "3                       A143    A153                                        1   \n", "4                       A143    A153                                        2   \n", "..                       ...     ...                                      ...   \n", "995                     A143    A152                                        1   \n", "996                     A143    A152                                        1   \n", "997                     A143    A152                                        1   \n", "998                     A143    A153                                        1   \n", "999                     A143    A152                                        1   \n", "\n", "      Job Number_of_people_being_liable_to_provide_maintenance_for  Telephone  \\\n", "0    A173                                                  1             A192   \n", "1    A173                                                  1             A191   \n", "2    A172                                                  2             A191   \n", "3    A173                                                  2             A191   \n", "4    A173                                                  2             A191   \n", "..    ...                                                ...              ...   \n", "995  A172                                                  1             A191   \n", "996  A174                                                  1             A192   \n", "997  A173                                                  1             A191   \n", "998  A173                                                  1             A192   \n", "999  A173                                                  1             A191   \n", "\n", "    foreign_worker  class bin bins  \n", "0             A201      0   5    F  \n", "1             A201      1   0    A  \n", "2             A201      0   4    E  \n", "3             A201      0   4    E  \n", "4             A201      1   5    F  \n", "..             ...    ...  ..  ...  \n", "995           A201      0   2    C  \n", "996           A201      0   4    E  \n", "997           A201      0   4    E  \n", "998           A201      1   0    A  \n", "999           A201      0   1    B  \n", "\n", "[1000 rows x 23 columns]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\959822559.py:42: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n"]}], "source": ["\n", "\n", "# 定义函数：根据分箱结果重新构建DataFrame，并将分箱区间改为字母标签\n", "def binning_transform(df: pd.DataFrame, feature: str, target: str, nan: float = -999.) -> pd.DataFrame:\n", "    \"\"\"\n", "    根据分箱结果重新构建DataFrame，将特征值替换为分箱区间（字母标签）\n", "    \"\"\"\n", "    boundary = optimal_binning_boundary(df[feature], df[target], nan)  # 获取分箱边界\n", "    df['bins'] = pd.cut(x=df[feature], bins=boundary, right=False)  # 分箱\n", "\n", "    # 将分箱区间改为字母标签\n", "    labels = [chr(65 + i) for i in range(len(boundary) - 1)]  # 生成 A, B, C, ...\n", "    df['bins'] = pd.cut(x=df[feature], bins=boundary, labels=labels, right=False)  # 使用字母标签\n", "\n", "    return df\n", "\n", "# 示例数据\n", "\n", "\n", "# 计算WOE和IV值\n", "result = feature_woe_iv(df['Age'], df['class'])\n", "print(result)\n", "\n", "# 根据分箱结果重新构建DataFrame\n", "transformed_df = binning_transform(df, 'Age', 'class')\n", "print(transformed_df)"]}, {"cell_type": "code", "execution_count": 8, "id": "7211c467-7205-48d4-a24f-a6b80a7e3119", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["该变量IV = 0.6660115033513336\n", "该变量IV = 0.2918298548787241\n", "该变量IV = 0.15079874288442632\n", "该变量IV = 0.19247255437629318\n", "该变量IV = 0.086433631026641\n", "该变量IV = 0.026322090054334526\n", "该变量IV = 0.044670***********\n", "该变量IV = 0.016420280451854008\n", "该变量IV = 0.0035887731887050208\n", "该变量IV = 0.*****************\n", "该变量IV = 0.057592071822288274\n", "该变量IV = 0.*****************\n", "该变量IV = 0.010083556765108678\n", "该变量IV = 0.008095049985707675\n", "该变量IV = 4.339222702973195e-05\n", "该变量IV = 0.0063776050286746735\n", "该变量IV = 0.0\n", "该变量IV = 0.*****************\n", "该变量IV = 0.*****************\n", "     Status_of_existing_checking_account  Duration  Credit_history   Purpose  \\\n", "0                              -0.818099         6        0.733741  0.410063   \n", "1                              -0.401392        48       -0.088319  0.410063   \n", "2                               1.176263        12        0.733741 -0.461635   \n", "3                              -0.818099        42       -0.088319 -0.122898   \n", "4                              -0.818099        24       -0.085158 -0.359200   \n", "..                                   ...       ...             ...       ...   \n", "995                             1.176263        12       -0.088319 -0.122898   \n", "996                            -0.818099        30       -0.088319  0.773836   \n", "997                             1.176263        12       -0.088319  0.410063   \n", "998                            -0.818099        45       -0.088319  0.410063   \n", "999                            -0.401392        45        0.733741  0.773836   \n", "\n", "     Credit_amount  Savings_account/bonds  Present_employment_since  \\\n", "0             1169               0.704246                  0.235566   \n", "1             5951              -0.271358                 -0.032103   \n", "2             2096              -0.271358                  0.394415   \n", "3             7882              -0.271358                  0.394415   \n", "4             4870              -0.271358                 -0.032103   \n", "..             ...                    ...                       ...   \n", "995           1736              -0.271358                  0.394415   \n", "996           3857              -0.271358                 -0.032103   \n", "997            804              -0.271358                  0.235566   \n", "998           1845              -0.271358                 -0.032103   \n", "999           4576              -0.139552                 -0.319230   \n", "\n", "     Installment_rate_in_percentage_of_disposable_income  \\\n", "0                                            -0.157300     \n", "1                                             0.155466     \n", "2                                             0.155466     \n", "3                                             0.155466     \n", "4                                             0.064539     \n", "..                                                 ...     \n", "995                                           0.064539     \n", "996                                          -0.157300     \n", "997                                          -0.157300     \n", "998                                          -0.157300     \n", "999                                           0.064539     \n", "\n", "     Personal_status_and_sex  Other_debtors/guarantors  ...  \\\n", "0                   0.165548                 -0.027974  ...   \n", "1                  -0.235341                 -0.027974  ...   \n", "2                   0.165548                 -0.027974  ...   \n", "3                   0.165548                  0.587787  ...   \n", "4                   0.165548                 -0.027974  ...   \n", "..                       ...                       ...  ...   \n", "995                -0.235341                 -0.027974  ...   \n", "996                -0.441833                 -0.027974  ...   \n", "997                 0.165548                 -0.027974  ...   \n", "998                 0.165548                 -0.027974  ...   \n", "999                 0.165548                 -0.027974  ...   \n", "\n", "     Other_installment_plans   Housing  \\\n", "0                   0.121179  0.194156   \n", "1                   0.121179  0.194156   \n", "2                   0.121179  0.194156   \n", "3                   0.121179 -0.472604   \n", "4                   0.121179 -0.472604   \n", "..                       ...       ...   \n", "995                 0.121179  0.194156   \n", "996                 0.121179  0.194156   \n", "997                 0.121179  0.194156   \n", "998                 0.121179 -0.472604   \n", "999                 0.121179  0.194156   \n", "\n", "     Number_of_existing_credits_at_this_bank       Job  \\\n", "0                                   0.134781  0.022780   \n", "1                                  -0.074877  0.022780   \n", "2                                  -0.074877  0.078472   \n", "3                                  -0.074877  0.022780   \n", "4                                   0.134781  0.022780   \n", "..                                       ...       ...   \n", "995                                -0.074877  0.078472   \n", "996                                -0.074877 -0.204413   \n", "997                                -0.074877  0.022780   \n", "998                                -0.074877  0.022780   \n", "999                                -0.074877  0.022780   \n", "\n", "     Number_of_people_being_liable_to_provide_maintenance_for  Telephone  \\\n", "0                                            -0.002816          0.098638   \n", "1                                            -0.002816         -0.064691   \n", "2                                             0.015409         -0.064691   \n", "3                                             0.015409         -0.064691   \n", "4                                             0.015409         -0.064691   \n", "..                                                 ...               ...   \n", "995                                          -0.002816         -0.064691   \n", "996                                          -0.002816          0.098638   \n", "997                                          -0.002816         -0.064691   \n", "998                                          -0.002816          0.098638   \n", "999                                          -0.002816         -0.064691   \n", "\n", "     foreign_worker  class       bin      bins  \n", "0               0.0      0 -0.009901 -0.009901  \n", "1               0.0      1 -0.528844 -0.528844  \n", "2               0.0      0  0.295117  0.295117  \n", "3               0.0      0  0.295117  0.295117  \n", "4               0.0      1 -0.009901 -0.009901  \n", "..              ...    ...       ...       ...  \n", "995             0.0      0 -0.142455 -0.142455  \n", "996             0.0      0  0.295117  0.295117  \n", "997             0.0      0  0.295117  0.295117  \n", "998             0.0      1 -0.528844 -0.528844  \n", "999             0.0      0  0.160930  0.160930  \n", "\n", "[1000 rows x 23 columns]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\2361643533.py:46: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\2361643533.py:46: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\2361643533.py:46: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\2361643533.py:46: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\2361643533.py:46: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\2361643533.py:46: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\2361643533.py:46: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\2361643533.py:46: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\2361643533.py:46: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\2361643533.py:46: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\2361643533.py:46: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\2361643533.py:46: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\2361643533.py:46: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\2361643533.py:46: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\2361643533.py:46: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\2361643533.py:46: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\2361643533.py:46: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\2361643533.py:46: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6276\\2361643533.py:46: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.preprocessing import LabelEncoder\n", "\n", "# 定义最优分箱边界值函数\n", "def optimal_binning_boundary(x: pd.Series, y: pd.Series, nan: float = -999.) -> list:\n", "    \"\"\"\n", "    利用决策树获得最优分箱的边界值列表\n", "    \"\"\"\n", "    boundary = []  # 分箱边界值列表\n", "    x = x.fillna(nan).values  # 填充缺失值\n", "    y = y.values\n", "\n", "    clf = DecisionTreeClassifier(criterion='entropy',  # 使用信息熵作为划分准则\n", "                                 max_leaf_nodes=6,     # 最大叶子节点数\n", "                                 min_samples_leaf=0.05)  # 叶子节点样本数量最小占比\n", "    clf.fit(x.reshape(-1, 1), y)  # 训练决策树\n", "\n", "    n_nodes = clf.tree_.node_count\n", "    children_left = clf.tree_.children_left\n", "    children_right = clf.tree_.children_right\n", "    threshold = clf.tree_.threshold\n", "\n", "    for i in range(n_nodes):\n", "        if children_left[i] != children_right[i]:  # 获得决策树节点上的划分边界值\n", "            boundary.append(threshold[i])\n", "\n", "    boundary.sort()\n", "    min_x = x.min()\n", "    max_x = x.max() + 0.1  # +0.1是为了包含特征最大值的样本\n", "    boundary = [min_x] + boundary + [max_x]\n", "    return boundary\n", "\n", "# 定义计算WOE和IV值的函数\n", "def feature_woe_iv(x: pd.Series, y: pd.Series, nan: float = -999.) -> pd.DataFrame:\n", "    \"\"\"\n", "    计算变量各个分箱的WOE、IV值，返回一个DataFrame\n", "    \"\"\"\n", "    x = x.fillna(nan)\n", "    boundary = optimal_binning_boundary(x, y, nan)  # 获得最优分箱边界值列表\n", "    df = pd.concat([x, y], axis=1)  # 合并x、y为一个DataFrame\n", "    df.columns = ['x', 'y']  # 重命名字段\n", "    df['bins'] = pd.cut(x=x, bins=boundary, right=False)  # 获得每个x值所在的分箱区间\n", "\n", "    grouped = df.groupby('bins')['y']  # 统计各分箱区间的好、坏、总客户数量\n", "    result_df = grouped.agg([('good', lambda y: (y == 0).sum()),\n", "                             ('bad', lambda y: (y == 1).sum()),\n", "                             ('total', 'count')])\n", "\n", "    result_df['good_pct'] = result_df['good'] / result_df['good'].sum()  # 好客户占比\n", "    result_df['bad_pct'] = result_df['bad'] / result_df['bad'].sum()  # 坏客户占比\n", "    result_df['total_pct'] = result_df['total'] / result_df['total'].sum()  # 总客户占比\n", "\n", "    result_df['bad_rate'] = result_df['bad'] / result_df['total']  # 坏比率\n", "    result_df['woe'] = np.log(result_df['good_pct'] / result_df['bad_pct'])  # WOE\n", "    result_df['iv'] = (result_df['good_pct'] - result_df['bad_pct']) * result_df['woe']  # IV\n", "\n", "    print(f\"该变量IV = {result_df['iv'].sum()}\")  # 输出总IV值\n", "    return result_df\n", "\n", "# 定义函数：对非连续变量进行WOE编码\n", "def woe_encode_categorical(df: pd.DataFrame, target: str, nan: float = -999.) -> pd.DataFrame:\n", "    \"\"\"\n", "    识别DataFrame中的非连续变量，并对这些变量进行WOE编码\n", "    \"\"\"\n", "    # 识别非连续变量（非数值类型或唯一值较少的数值类型）\n", "    categorical_columns = []\n", "    for col in df.columns:\n", "        if col != target:  # 排除目标变量\n", "            if df[col].dtype == 'object' or df[col].nunique() < 10:  # 非数值类型或唯一值较少\n", "                categorical_columns.append(col)\n", "\n", "    # 对每个非连续变量进行WOE编码\n", "    woe_encoded_df = df.copy()\n", "    for col in categorical_columns:\n", "        # 如果是非连续变量，先进行标签编码\n", "        if df[col].dtype == 'object':\n", "            label_encoder = LabelEncoder()\n", "            df[col] = label_encoder.fit_transform(df[col])\n", "        elif df[col].dtype == 'category':\n", "            # 如果是分类变量，先转换为普通类型\n", "            df[col] = df[col].astype(str)\n", "            label_encoder = LabelEncoder()\n", "            df[col] = label_encoder.fit_transform(df[col])\n", "        \n", "        woe_df = feature_woe_iv(df[col], df[target], nan)\n", "        woe_mapping = woe_df['woe'].to_dict()\n", "        woe_encoded_df[col] = df[col].map(woe_mapping).fillna(woe_df['woe'].mean())  # 替换为WOE值，缺失值用平均WOE填充\n", "\n", "    return woe_encoded_df\n", "\n", "\n", "\n", "# 对非连续变量进行WOE编码\n", "woe_encoded_df = woe_encode_categorical(df, target='class')\n", "print(woe_encoded_df)"]}, {"cell_type": "code", "execution_count": 9, "id": "1a21d927-9bfd-4c2e-856f-1d573ce8b727", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["保留的特征: ['Status_of_existing_checking_account', 'Duration', 'Credit_history', 'Purpose', 'Credit_amount', 'Savings_account/bonds', 'Present_employment_since', 'Property', 'Age', 'Other_installment_plans', 'Housing', 'class', 'bin', 'bins']\n", "删除的特征: ['Installment_rate_in_percentage_of_disposable_income', 'Personal_status_and_sex', 'Other_debtors/guarantors', 'Present_residence_since', 'Number_of_existing_credits_at_this_bank', 'Job', 'Number_of_people_being_liable_to_provide_maintenance_for', 'Telephone', 'foreign_worker']\n", "\n", "每个特征的方差：\n", "Status_of_existing_checking_account                         7.583718e-01\n", "Duration                                                    1.454150e+02\n", "Credit_history                                              2.957670e-01\n", "Purpose                                                     1.609085e-01\n", "Credit_amount                                               7.967843e+06\n", "Savings_account/bonds                                       2.178391e-01\n", "Present_employment_since                                    8.571372e-02\n", "Installment_rate_in_percentage_of_disposable_income         2.660262e-02\n", "Personal_status_and_sex                                     4.368685e-02\n", "Other_debtors/guarantors                                    1.870983e-02\n", "Present_residence_since                                     3.613770e-03\n", "Property                                                    1.130543e-01\n", "Age                                                         1.294013e+02\n", "Other_installment_plans                                     5.432918e-02\n", "Housing                                                     8.013532e-02\n", "Number_of_existing_credits_at_this_bank                     1.022181e-02\n", "Job                                                         7.883635e-03\n", "Number_of_people_being_liable_to_provide_maintenance_for    4.354571e-05\n", "Telephone                                                   6.429664e-03\n", "foreign_worker                                              0.000000e+00\n", "class                                                       2.102102e-01\n", "bin                                                         1.445518e-01\n", "bins                                                        1.445518e-01\n", "dtype: float64\n", "\n", "被删除特征的方差：\n", "Installment_rate_in_percentage_of_disposable_income         0.026603\n", "Personal_status_and_sex                                     0.043687\n", "Other_debtors/guarantors                                    0.018710\n", "Present_residence_since                                     0.003614\n", "Number_of_existing_credits_at_this_bank                     0.010222\n", "Job                                                         0.007884\n", "Number_of_people_being_liable_to_provide_maintenance_for    0.000044\n", "Telephone                                                   0.006430\n", "foreign_worker                                              0.000000\n", "dtype: float64\n"]}], "source": ["import pandas as pd\n", "from sklearn.feature_selection import VarianceThreshold\n", "\n", "\n", "\n", "# 设置方差阈值\n", "threshold = 0.05\n", "\n", "# 初始化 VarianceT<PERSON><PERSON>old\n", "selector = VarianceThreshold(threshold=threshold)\n", "\n", "# 应用 V<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "selector.fit(woe_encoded_df)\n", "\n", "# 获取被保留的特征索引\n", "selected_features_indices = selector.get_support(indices=True)\n", "\n", "# 获取被删除的特征索引\n", "removed_features_indices = [i for i in range(woe_encoded_df.shape[1]) if i not in selected_features_indices]\n", "\n", "# 获取特征名称\n", "selected_features = woe_encoded_df.columns[selected_features_indices]\n", "removed_features = woe_encoded_df.columns[removed_features_indices]\n", "\n", "# 输出结果\n", "print(f\"保留的特征: {selected_features.tolist()}\")\n", "print(f\"删除的特征: {removed_features.tolist()}\")\n", "\n", "# 输出每个特征的方差\n", "variances = woe_encoded_df.var()\n", "print(\"\\n每个特征的方差：\")\n", "print(variances)\n", "\n", "# 输出被删除特征的方差\n", "print(\"\\n被删除特征的方差：\")\n", "print(variances[removed_features])\n", "selected_df = woe_encoded_df.drop(removed_features, axis=1)"]}, {"cell_type": "code", "execution_count": 10, "id": "a66cef80-7b18-421f-98a6-bd81ce66fe50", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["保留的特征: ['Status_of_existing_checking_account', 'Duration', 'Credit_history', 'Purpose', 'Credit_amount', 'Savings_account/bonds', 'Present_employment_since', 'Property', 'Age', 'Other_installment_plans', 'Housing', 'class', 'bin', 'bins']\n", "删除的特征: ['Installment_rate_in_percentage_of_disposable_income', 'Personal_status_and_sex', 'Other_debtors/guarantors', 'Present_residence_since', 'Number_of_existing_credits_at_this_bank', 'Job', 'Number_of_people_being_liable_to_provide_maintenance_for', 'Telephone', 'foreign_worker']\n", "\n", "每个特征的方差：\n", "Status_of_existing_checking_account                         7.583718e-01\n", "Duration                                                    1.454150e+02\n", "Credit_history                                              2.957670e-01\n", "Purpose                                                     1.609085e-01\n", "Credit_amount                                               7.967843e+06\n", "Savings_account/bonds                                       2.178391e-01\n", "Present_employment_since                                    8.571372e-02\n", "Installment_rate_in_percentage_of_disposable_income         2.660262e-02\n", "Personal_status_and_sex                                     4.368685e-02\n", "Other_debtors/guarantors                                    1.870983e-02\n", "Present_residence_since                                     3.613770e-03\n", "Property                                                    1.130543e-01\n", "Age                                                         1.294013e+02\n", "Other_installment_plans                                     5.432918e-02\n", "Housing                                                     8.013532e-02\n", "Number_of_existing_credits_at_this_bank                     1.022181e-02\n", "Job                                                         7.883635e-03\n", "Number_of_people_being_liable_to_provide_maintenance_for    4.354571e-05\n", "Telephone                                                   6.429664e-03\n", "foreign_worker                                              0.000000e+00\n", "class                                                       2.102102e-01\n", "bin                                                         1.445518e-01\n", "bins                                                        1.445518e-01\n", "dtype: float64\n", "\n", "被删除特征的方差：\n", "Installment_rate_in_percentage_of_disposable_income         0.026603\n", "Personal_status_and_sex                                     0.043687\n", "Other_debtors/guarantors                                    0.018710\n", "Present_residence_since                                     0.003614\n", "Number_of_existing_credits_at_this_bank                     0.010222\n", "Job                                                         0.007884\n", "Number_of_people_being_liable_to_provide_maintenance_for    0.000044\n", "Telephone                                                   0.006430\n", "foreign_worker                                              0.000000\n", "dtype: float64\n", "\n", "基于相关性选择的特征: ['Status_of_existing_checking_account', 'Duration', 'Credit_history', 'Purpose', 'Credit_amount', 'Savings_account/bonds', 'Present_employment_since', 'Property', 'Other_installment_plans', 'Housing', 'bin', 'bins']\n", "\n", "每个特征的 VIF 值：\n", "                                feature       VIF\n", "0   Status_of_existing_checking_account  1.174014\n", "1                              Duration  5.799547\n", "2                        Credit_history  1.132765\n", "3                               Purpose  1.046798\n", "4                         Credit_amount  4.002581\n", "5                 Savings_account/bonds  1.086018\n", "6              Present_employment_since  1.067504\n", "7                              Property  1.293760\n", "8                                   Age  3.606237\n", "9               Other_installment_plans  1.053276\n", "10                              Housing  1.228084\n", "11                                  bin       inf\n", "12                                 bins       inf\n", "\n", "VIF 值高于 10 的特征，可能需要被移除: ['bin', 'bins']\n", "\n", "最终保留的特征：\n", "['Status_of_existing_checking_account', 'Duration', 'Credit_history', 'Purpose', 'Credit_amount', 'Savings_account/bonds', 'Present_employment_since', 'Property', 'Age', 'Other_installment_plans', 'Housing', 'class']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["D:\\anancoda\\Lib\\site-packages\\statsmodels\\stats\\outliers_influence.py:197: RuntimeWarning: divide by zero encountered in scalar divide\n", "  vif = 1. / (1. - r_squared_i)\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.feature_selection import SelectKBest, f_classif\n", "from statsmodels.stats.outliers_influence import variance_inflation_factor\n", "\n", "\n", "# 设置方差阈值\n", "threshold = 0.05\n", "\n", "# 初始化 VarianceT<PERSON><PERSON>old\n", "selector = VarianceThreshold(threshold=threshold)\n", "\n", "# 应用 V<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "selector.fit(woe_encoded_df)\n", "\n", "# 获取被保留的特征索引\n", "selected_features_indices = selector.get_support(indices=True)\n", "\n", "# 获取被删除的特征索引\n", "removed_features_indices = [i for i in range(woe_encoded_df.shape[1]) if i not in selected_features_indices]\n", "\n", "# 获取特征名称\n", "selected_features = woe_encoded_df.columns[selected_features_indices]\n", "removed_features = woe_encoded_df.columns[removed_features_indices]\n", "\n", "# 输出结果\n", "print(f\"保留的特征: {selected_features.tolist()}\")\n", "print(f\"删除的特征: {removed_features.tolist()}\")\n", "\n", "# 输出每个特征的方差\n", "variances = woe_encoded_df.var()\n", "print(\"\\n每个特征的方差：\")\n", "print(variances)\n", "\n", "# 输出被删除特征的方差\n", "print(\"\\n被删除特征的方差：\")\n", "print(variances[removed_features])\n", "\n", "# 从原始数据中删除方差低于阈值的特征\n", "selected_df = woe_encoded_df.drop(removed_features, axis=1)\n", "\n", "# 分离特征和目标变量\n", "X = selected_df.drop(columns=['class'])\n", "y = selected_df['class']\n", "\n", "# 1. 基于相关性选择特征\n", "# 使用 SelectKBest 和 f_classif 选择与目标变量相关性最高的 K 个特征\n", "k = 12  # 选择得分最高的 12 个特征\n", "selector = SelectKBest(score_func=f_classif, k=min(k, X.shape[1]))  # 确保 k 不超过特征数量\n", "selector.fit(X, y)\n", "\n", "# 获取被保留的特征索引\n", "selected_features_indices = selector.get_support(indices=True)\n", "selected_features = X.columns[selected_features_indices]\n", "\n", "# 输出相关性选择的结果\n", "print(f\"\\n基于相关性选择的特征: {selected_features.tolist()}\")\n", "\n", "# 2. 检测共线性\n", "# 计算 VIF\n", "vif_data = pd.DataFrame()\n", "vif_data[\"feature\"] = X.columns\n", "vif_data[\"VIF\"] = [variance_inflation_factor(X.values, i) for i in range(X.shape[1])]\n", "\n", "# 输出 VIF 值\n", "print(\"\\n每个特征的 VIF 值：\")\n", "print(vif_data)\n", "\n", "# 根据 VIF 阈值筛选特征\n", "vif_threshold = 10\n", "high_vif_features = vif_data[vif_data[\"VIF\"] > vif_threshold][\"feature\"].tolist()\n", "\n", "print(f\"\\nVIF 值高于 {vif_threshold} 的特征，可能需要被移除: {high_vif_features}\")\n", "\n", "# 从相关性筛选后的数据中删除 VIF 高的特征\n", "final_selected_df = selected_df.drop(high_vif_features, axis=1)\n", "\n", "print(\"\\n最终保留的特征：\")\n", "print(final_selected_df.columns.tolist())"]}, {"cell_type": "code", "execution_count": 11, "id": "9f901c3f-b40a-4014-9c98-2ad03d08a036", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fitting estimator with 11 features.\n", "Fitting estimator with 10 features.\n", "Fitting estimator with 9 features.\n", "Fitting estimator with 8 features.\n", "Fitting estimator with 7 features.\n", "Fitting estimator with 6 features.\n", "Fitting estimator with 5 features.\n", "Fitting estimator with 4 features.\n", "Fitting estimator with 3 features.\n", "Fitting estimator with 2 features.\n", "Fitting estimator with 11 features.\n", "Fitting estimator with 10 features.\n", "Fitting estimator with 9 features.\n", "Fitting estimator with 8 features.\n", "Fitting estimator with 7 features.\n", "Fitting estimator with 6 features.\n", "Fitting estimator with 5 features.\n", "Fitting estimator with 4 features.\n", "Fitting estimator with 3 features.\n", "Fitting estimator with 2 features.\n", "Fitting estimator with 11 features.\n", "Fitting estimator with 10 features.\n", "Fitting estimator with 9 features.\n", "Fitting estimator with 8 features.\n", "Fitting estimator with 7 features.\n", "Fitting estimator with 6 features.\n", "Fitting estimator with 5 features.\n", "Fitting estimator with 4 features.\n", "Fitting estimator with 3 features.\n", "Fitting estimator with 2 features.\n", "Fitting estimator with 11 features.\n", "Fitting estimator with 10 features.\n", "Fitting estimator with 9 features.\n", "Fitting estimator with 8 features.\n", "Fitting estimator with 7 features.\n", "Fitting estimator with 6 features.\n", "Fitting estimator with 5 features.\n", "Fitting estimator with 4 features.\n", "Fitting estimator with 3 features.\n", "Fitting estimator with 2 features.\n", "Fitting estimator with 11 features.\n", "Fitting estimator with 10 features.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["D:\\anancoda\\Lib\\site-packages\\sklearn\\linear_model\\_logistic.py:470: ConvergenceWarning: lbfgs failed to converge after 1000 iteration(s) (status=1):\n", "STOP: TOTAL NO. of ITERATIONS REACHED LIMIT\n", "\n", "Increase the number of iterations to improve the convergence (max_iter=1000).\n", "You might also want to scale the data as shown in:\n", "    https://scikit-learn.org/stable/modules/preprocessing.html\n", "Please also refer to the documentation for alternative solver options:\n", "    https://scikit-learn.org/stable/modules/linear_model.html#logistic-regression\n", "  n_iter_i = _check_optimize_result(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Fitting estimator with 9 features.\n", "Fitting estimator with 8 features.\n", "Fitting estimator with 7 features.\n", "Fitting estimator with 6 features.\n", "Fitting estimator with 5 features.\n", "Fitting estimator with 4 features.\n", "Fitting estimator with 3 features.\n", "Fitting estimator with 2 features.\n", "Fitting estimator with 11 features.\n", "Fitting estimator with 10 features.\n", "\n", "RFECV 选择的特征：\n", "['Status_of_existing_checking_account', 'Duration', 'Credit_history', 'Purpose', 'Savings_account/bonds', 'Present_employment_since', 'Property', 'Other_installment_plans', 'Housing']\n", "\n", "每个特征的排名：\n", "Status_of_existing_checking_account    1\n", "Duration                               1\n", "Credit_history                         1\n", "Purpose                                1\n", "Credit_amount                          3\n", "Savings_account/bonds                  1\n", "Present_employment_since               1\n", "Property                               1\n", "Age                                    2\n", "Other_installment_plans                1\n", "Housing                                1\n", "dtype: int32\n", "\n", "最优特征数量：9\n"]}], "source": ["from sklearn.feature_selection import RFECV\n", "from sklearn.linear_model import LogisticRegression\n", "\n", "# 初始化模型（这里使用逻辑回归作为示例）\n", "estimator = LogisticRegression(max_iter=1000)\n", "\n", "# 初始化 RFECV\n", "rfecv = RFECV(estimator=estimator, step=1, min_features_to_select=1, cv=5, scoring='accuracy', verbose=1)\n", "\n", "# 分离特征和目标变量\n", "X = final_selected_df.drop(columns=['class'])  # 特征数据\n", "y = final_selected_df['class']  # 目标变量\n", "\n", "# 应用 RFECV\n", "rfecv.fit(X, y)\n", "\n", "# 获取被保留的特征\n", "rfecv_selected_features = X.columns[rfecv.support_]\n", "\n", "# 输出结果\n", "print(\"\\nRFECV 选择的特征：\")\n", "print(rfecv_selected_features.tolist())\n", "\n", "# 输出每个特征的排名\n", "print(\"\\n每个特征的排名：\")\n", "print(pd.Series(rfecv.ranking_, index=X.columns))\n", "\n", "# 输出最优特征数量\n", "print(f\"\\n最优特征数量：{rfecv.n_features_}\")"]}, {"cell_type": "code", "execution_count": 14, "id": "b53137df-e741-4788-8fcf-cc28e07c3270", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 0.12117862, -0.47755083])"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["final_selected_df['Other_installment_plans'].unique()"]}, {"cell_type": "code", "execution_count": 15, "id": "30d27f3d-3e6e-44d3-8377-ae99ce9ef90e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模型准确率: 0.78\n", "\n", "混淆矩阵：\n", "[[128  13]\n", " [ 32  27]]\n", "\n", "分类报告：\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.80      0.91      0.85       141\n", "           1       0.68      0.46      0.55        59\n", "\n", "    accuracy                           0.78       200\n", "   macro avg       0.74      0.68      0.70       200\n", "weighted avg       0.76      0.78      0.76       200\n", "\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import accuracy_score, classification_report, confusion_matrix\n", "\n", "\n", "# 分离特征和目标变量\n", "X = final_selected_df.drop(columns=['class'])\n", "y = final_selected_df['class']\n", "\n", "# 划分训练集和测试集\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# 初始化逻辑回归模型\n", "logistic_model = LogisticRegression(max_iter=1000)\n", "\n", "# 训练模型\n", "logistic_model.fit(X_train, y_train)\n", "\n", "# 进行预测\n", "y_pred = logistic_model.predict(X_test)\n", "\n", "# 评估模型\n", "accuracy = accuracy_score(y_test, y_pred)\n", "conf_matrix = confusion_matrix(y_test, y_pred)\n", "class_report = classification_report(y_test, y_pred)\n", "\n", "print(f\"模型准确率: {accuracy:.2f}\")\n", "print(\"\\n混淆矩阵：\")\n", "print(conf_matrix)\n", "print(\"\\n分类报告：\")\n", "print(class_report)"]}, {"cell_type": "code", "execution_count": 18, "id": "245f66a2-bde9-4831-aa38-dd48c4276c61", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模型准确率: 0.78\n", "\n", "混淆矩阵：\n", "[[128  13]\n", " [ 32  27]]\n", "\n", "分类报告：\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.80      0.91      0.85       141\n", "           1       0.68      0.46      0.55        59\n", "\n", "    accuracy                           0.78       200\n", "   macro avg       0.74      0.68      0.70       200\n", "weighted avg       0.76      0.78      0.76       200\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "AUC: 0.80\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_curve, roc_auc_score\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "\n", "# 分离特征和目标变量\n", "X = final_selected_df.drop(columns=['class'])\n", "y = final_selected_df['class']\n", "\n", "# 划分训练集和测试集\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# 初始化逻辑回归模型\n", "logistic_model = LogisticRegression(max_iter=1000)\n", "\n", "# 训练模型\n", "logistic_model.fit(X_train, y_train)\n", "\n", "# 进行预测\n", "y_pred = logistic_model.predict(X_test)\n", "y_pred_proba = logistic_model.predict_proba(X_test)[:, 1]  # 预测正类的概率\n", "\n", "# 评估模型\n", "accuracy = accuracy_score(y_test, y_pred)\n", "conf_matrix = confusion_matrix(y_test, y_pred)\n", "class_report = classification_report(y_test, y_pred)\n", "\n", "print(f\"模型准确率: {accuracy:.2f}\")\n", "print(\"\\n混淆矩阵：\")\n", "print(conf_matrix)\n", "print(\"\\n分类报告：\")\n", "print(class_report)\n", "\n", "# 计算 ROC 曲线和 AUC\n", "fpr, tpr, thresholds = roc_curve(y_test, y_pred_proba)\n", "roc_auc = roc_auc_score(y_test, y_pred_proba)\n", "\n", "# 绘制 ROC 曲线\n", "plt.figure(figsize=(8, 6))\n", "plt.plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC curve (area = {roc_auc:.2f})')\n", "plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')\n", "plt.xlim([0.0, 1.0])\n", "plt.ylim([0.0, 1.05])\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "plt.title('Receiver Operating Characteristic (ROC) Curve')\n", "plt.legend(loc=\"lower right\")\n", "plt.show()\n", "\n", "print(f\"\\nAUC: {roc_auc:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "c6240cb1-c39a-41db-8354-e2ea4bdf9c6c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}