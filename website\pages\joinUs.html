<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>数投科技金融</title>
    <meta http-equiv="keywords" content="数投科技金融,股票,智能投顾,大数据,人工智能,金融创新,金融投资,投资理财" />
    <meta http-equiv="description" content="数投科技金融是以量化投资研究为核心竞争力，脱胎于曾经国内业绩排名第一的量化投资团队，专注于人工智能投资技术开发的金融科技公司。" />
    <meta name="Resource-type" content="Document" />


    <link rel="stylesheet" type="text/css" href="../static/fullpage/fullpage.min.css" />
    <link rel="stylesheet" type="text/css" href="../style/coumon.css" />

    <script src="../static/jquery/1.9.1/jquery.min.js"></script>
    <script src="../static/fullpage/scrolloverflow.min.js"></script>
    <script src="../static/fullpage/fullpage.min.js"></script>
    <style>
        .section {
            text-align: center;
        }


        /* Backgrounds will cover all the section
        * --------------------------------------- */
        .section {
            background-size: cover;
        }

        .slide {
            background-size: cover;
        }

        #toljs {
            font-size: 14px;
        }

        .firstSesion {
            padding-top: 100px;
            width: 100%;
            background-image: url(../images/joinus.jpg);
            /*background-size: 100% 100%;*/
            background-size: cover;
        }

        .concentRow {
            padding-top: 70px;
            width: 1150px;
            justify-content: space-between;
            margin: 0 auto;
        }

        .concentRow .left {
            width: 428px;
        }

        .concentRow .left img {
            width: 660px;
            height: 440px;
        }

        .concentRow .right {
            width: 350px;
            margin-left: 50px;
            margin-top: 100px;
        }

        .concentRow .right .ritghtCon {
            font-size: 16px;
            color: rgba(0, 0, 0, .7);
            word-break: break-all;
            text-align: left;
            line-height: 26px;
        }

        .wordContent ul {
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            justify-content: space-between;
            width: 1150px;
            margin: 20px auto;

        }

        .wordContent ul li {
            padding: 10px;
            color: #000;
        }

        .wordContent ul .active {
            border-bottom: 2px solid #CBAE60;
        }

        #section2 .concentRow .left img {
            width: 200px;
            height: 380px;
        }

        #section2 .concentRow .left {
            padding-bottom: 150px;
        }

        .tryUse {
            width: 100%;
            height: 114px;
            background-color: #223457;
            color: #fff;
            position: absolute;
            bottom: 0;
        }

        .tryUse a {
            display: inline-block;
            width: 220px;
            height: 66px;
            background-color: #CBAE60;
            color: #fff;
            border-radius: 32px;
            text-align: center;
            line-height: 65px;
            font-size: 22px;
            margin-top: 20px;
        }

        .componLog {
            width: 1150px;
            margin: 0 auto;
        }

        .componLog ul {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: space-between;
            width: 1150px;
            padding-top: 20px;
            /*padding-bottom: 200px;*/
        }

        .componLog ul li {
            margin: 18px 18px;
            width: 248px;
            height: 248px;
            color: #000;
        }

        .componLog ul li img {
            width: 250px;
            height: 250px;

        }

        .yanProduc {
            margin-bottom: 100px;
        }

        .yanProduc ul {
            width: 1150px;
            margin: 0 auto;
        }

        .yanProduc ul li {
            margin-top: 75px;
            width: 300px;
            height: 277px;
            padding: 30px 20px 15px;
            background-color: #fff;
            position: relative;
        }

        .yanProduc ul li img {
            width: 150px;
            height: 150px;
            display: inline-block;
        }

        .yanProduc ul li .proDesc {
            margin-top: 10px;
            font-size: 14px;
            color: rgba(0, 0, 0, .9);
            word-break: break-all;
            text-align: left;
            line-height: 22px;
        }

        .coverContent {
            width: 600px;
            height: 600px;
            margin-top: -300px;
            margin-left: -300px;
        }

        .contBox {
            padding: 0 60px;
        }

        .contentDes {
            font-size: 14px;
            color: #000;
            line-height: 26px;
        }

        .contentDes h4 {
            font-size: 16px;
            font-weight: 600;
        }

        .coverBtn {
            margin-top: 40px;
        }
    </style>
    <script src="../static/index.js"></script>

</head>

<body>
    <div class="coverPage">
        <div class="coverContent">
            <div class="close">
                ×
            </div>
            <div class="contBox">
                <div class="contentDes">
                    <h4>工作职责 :</h4>
                    1.带领团队完成产品的调研、分析及设计，跟踪、分析、挖掘产品的客户需求,并能结合公司整体的业务策略进行需求评估；
                    <br />2.根据市场需求和变化制定产品的产品开发计划，独立编写产品需求，结合用户需求提出产品优化及创新需求；
                    <br />3.确定产品功能、流程，用户体验方案，绘制产品原型图，编写相关需求文档； 负责项目的实施过程，协助研发和测试需求理解及需求验证，跟进推动项目实施，及项目发布
                    <br />
                    <br />
                    <h4>任职要求：</h4>
                    1.统招本科及以上学历；
                    <br />2.熟悉互联网平台产品的开发流程，并曾经组织过产品设计、研发、推广及改进整个过程；
                    <br />3.熟练使用产品原型设计，思维导图，文档数据管理等软件；
                    <br />4.出色的语言、沟通、组织和协调能力，极强的问题解决能力，执行力强；
                    <br />5.对产品设计应有自己独立理解，并善于聆听用户、团队和合作伙伴的声音
                </div>
                <div class="coverBtn">
                    <a>投递简历</a>
                </div>

            </div>
        </div>
    </div>
    <div id="fullpage">
        <div class="section" id="section0">
            <div class="header">
                <ul>
                    <li>
                        <a href="aboutYushi.html">关于数投科技</a>
                        <!--<div class="second-level">
                        <a href="../About_Us.aspx">关于基岩</a>
                        <a href="../Success_Case.aspx">成功案例</a>
                    </div>-->
                    </li>
                    <li>
                        <a>专业机构服务</a>
                        <div class="second-level">
                            <a href="javascript:void(0)" id="toKunpeng">鲲鹏财富管理</a>
                            <a href="AItouYan.html">AI投研</a>
                            <a href="kunpengAssetAllocation.html">鲲鹏资产配置助手</a>
                            <a href="shumunlessBase.html">数投无人基</a>
                            <a href="javascript:void(0)" id="toljs">炼金术系统</a>
                        </div>
                    </li>
                    <!--                <li>-->
                    <!--                    <a >个人服务</a>-->
                    <!--                    <div class="second-level">-->
                    <!--                        <a href="AIgu.html">AI股</a>-->
                    <!--                        <a href="AItouGu.html">AI投顾</a>-->
                    <!--                    </div>-->
                    <!--                </li>-->
                    <li class="middle-logo">
                        <a href="../index.html"></a>
                    </li>
                    <li>
                        <a href="touLaboratory.html">智能投顾实验室</a>

                    </li>
                    <li>
                        <a href="inforYushi.html">数投科技动态</a>
                    </li>
                    <li>
                        <a href="joinUs.html">加入我们</a>
                    </li>
                </ul>
            </div>
            <div class="intro firstSesion">

            </div>
        </div>
        <div class="section" id="section1">
            <div class="titles">
                公司福利
            </div>
            <div class="concentRow">
                <div class="left">
                    <img src="../images/roomUs.png" alt="" />
                </div>
                <div class="right">
                    <div class="ritghtCon">
                        1、五险一金
                        <br />2、带薪病假、带薪年假、带薪月假
                        <br />3、节假日红包、生日红包/礼物
                    </div>
                </div>
            </div>
        </div>
        <div class="section" id="section2" style="background-color: #F4F4F4;position: relative;">
            <div class="titles" style="padding-top:10px;">
                热招岗位
            </div>
            <div class="yanProduc">
                <ul class="conBetwoon">
                    <li>
                        <div class="conBetwoon"
                            style="font-size: 18px;border-bottom: 2px solid #FBFBFB;padding-bottom: 5px;">
                            <div>金融产品销售经理</div>
                        </div>
                        <div class="proDesc">
                            1、负责根据公司金融产品业务发展战略规划和年度重点工作，制定、提交销售业务的业务规划和工作安排，并组织实施；
                            <br />2、负责完成公司下达的金融产品销售任务目标；
                            <br />3、负责组织开展金融产品业务的营销推广和客户服务等工作；
                        </div>
                        <div style="position: absolute;right:20px;bottom:10px;">
                            <script>
                                var today = new Date();
                                var year = today.getFullYear();
                                var month = (today.getMonth() + 1).toString().padStart(2, "0");
                                var day = today.getDate().toString().padStart(2, "0");
                                document.write(year + "-" + month + "-" + day);
                            </script>
                        </div>
                    </li>
                    <li style="margin:75px 58px 0;">
                        <div class="conBetwoon"
                            style="font-size: 18px;border-bottom: 2px solid #FBFBFB;padding-bottom: 5px;">
                            <div>机构软件销售业务经理</div>
                        </div>
                        <div class="proDesc">
                            1、负责机构软件销售业务，配合公司总体机构软件销售策略和目标，实施机构销售策略计划和策略；
                            <br />2、负责机构客户的开拓、维护与服务工作，包括客户走访、安排公司投研人员路演、向客户提供投资建议与市场咨询，协助客户完成交易，举办各类讲座、研讨会和推介活动等；
                            <br />3、做好客户拜访和接待记录，会后客户跟踪工作，建立并完善客户数据库；
                        </div>
                        <div style="position: absolute;right:20px;bottom:10px;">
                            <script>
                                var today = new Date();
                                var year = today.getFullYear();
                                var month = (today.getMonth() + 1).toString().padStart(2, "0");
                                var day = today.getDate().toString().padStart(2, "0");
                                document.write(year + "-" + month + "-" + day);
                            </script>
                        </div>
                    </li>
                    <li>
                        <div class="conBetwoon"
                            style="font-size: 18px;border-bottom: 2px solid #FBFBFB;padding-bottom: 5px;">
                            <div>研究员助理（量化方向）</div>
                        </div>
                        <div class="proDesc">
                            1、协助投资分析团队进行交易策略，金工策略的构建建模；
                            <br />2、协助进行公司重组公告，新股，次新股等事件策略的研究；
                            <br />3、开发各类技术分析的量化交易策略；
                            <br />4、开发各类短线择时策略；
                        </div>
                        <div style="position: absolute;right:20px;bottom:10px;">
                            <script>
                                var today = new Date();
                                var year = today.getFullYear();
                                var month = (today.getMonth() + 1).toString().padStart(2, "0");
                                var day = today.getDate().toString().padStart(2, "0");
                                document.write(year + "-" + month + "-" + day);
                            </script>
                        </div>
                    </li>
                </ul>
            </div>

        </div>
        <div class="section" id="section3">
            <div class="intro">
                <div class="titles">
                    公司环境
                </div>
                <div class="componLog">
                    <ul>
                        <li>
                            <img src="../images/canpany1.png" alt="" />
                        </li>
                        <li>
                            <img src="../images/canpany2.png" alt="" />
                        </li>
                        <li>
                            <img src="../images/canpany3.png" alt="" />
                        </li>
                        <li>
                            <img src="../images/canpany4.png" alt="" />
                        </li>
                        <li>
                            <img src="../images/canpany5.png" alt="" />
                        </li>
                        <li>
                            <img src="../images/canpany6.png" alt="" />
                        </li>
                        <li>
                            <img src="../images/canpany7.png" alt="" />
                        </li>
                        <li>
                            <img src="../images/canpany8.png" alt="" />
                        </li>
                    </ul>
                    <div>
                    </div>
                </div>
            </div>
        </div>
        <div class="section" id="section4">

            <div class="footer">
                <div class="footerBox">
                    <div class="footOne">
                        <ul>
                            <li><a>关于数投科技</a></li>
                            <li><a href="aboutYushi.html" style="color:#FFFFFF">公司简介</a></li>
                            <li><a href="aboutYushi.html" style="color:#FFFFFF">公司理念</a></li>
                            <!-- <li><a >组织架构</a></li> -->
                            <li><a href="aboutYushi.html" style="color:#FFFFFF">管理团队</a></li>
                            <li><a href="aboutYushi.html" style="color:#FFFFFF">大事记</a></li>
                            <li><a href="aboutYushi.html" style="color:#FFFFFF">数投科技荣誉</a></li>
                        </ul>
                        <ul>
                            <li><a>数投科技动态</a></li>
                            <li><a href="inforYushi.html" style="color:#FFFFFF">公司新闻</a></li>
                            <li><a href="inforYushi.html" style="color:#FFFFFF">行业动态</a></li>
                        </ul>
                        <ul>
                            <li><a>加入数投科技</a></li>
                            <li><a href="joinUs.html" style="color:#FFFFFF">公司招聘</a></li>
                        </ul>
                    </div>
                    <div class="footTwo">
                        <ul>
                            <li><a>联系我们</a></li>
                            <li><img src="../images/Fill1.png" alt="" /><a>上海市浦东新区兰花路333号333世纪大厦603室</a></li>
                            <li><img src="../images/Fill12.png" alt="" /><a><EMAIL></a></li>
                            <li><img src="../images/Fill13.png" alt="" /><a>021-58896069</a></li>
                        </ul>
                    </div>
                    <!--                    <div class="footThree">-->
                    <!--                        <div class="erNo">-->
                    <!--                            <img src="images/weiNo.png" alt=""/>-->
                    <!--                        </div>-->
                    <!--                        <div class="erName">微信公众号</div>-->

                    <!--                    </div>-->
                    <div class="corpyMsg" style="margin-top:70px;">
                        <br />
                        <div style="text-align: center;width:100%;">
                            <a style="color: #ffffff;" href="./privacy.html">隐私政策</a>
                        </div>
                        <br />
                        <span style="color: #ffffff;">
                            Copyright 2024 by 51lianjin.com &nbsp;&nbsp; 上海智慧数投科技有限公司 &nbsp;&nbsp; <a
                                style="color: #ffffff;" href="https://beian.miit.gov.cn">沪ICP备15046324号-2</a>
                        </span>
                    </div>
                </div>


            </div>

        </div>
    </div>

    <script type="text/javascript">
        var myFullpage = new fullpage('#fullpage', {
            anchors: ['firstPage', 'secondPage', '3rdPage', "4rtPage", "5thPage"],
            sectionsColor: [],
            navigation: false,
            navigationPosition: '',
            navigationTooltips: ['First page', 'Second page', 'Third and last page'],
            responsiveWidth: 900,
            scrollOverflow: true,
            afterResponsive: function (isResponsive) {

            },
            afterLoad: function (anchorLink, index) {
                if (index == 2) {
                    $('.section2').find('p').delay(500).animate({
                        left: '0'
                    }, 1500, 'easeOutExpo');
                }
                if (index == 3) {
                    $('.section3').find('p').delay(500).animate({
                        bottom: '0'
                    }, 1500, 'easeOutExpo');
                }
                if (index == 4) {
                    $('.section4').find('p').fadeIn(2000);
                }
            },
            onLeave: function (index, direction) {
                if (index == '2') {
                    $('.section2').find('p').delay(500).animate({
                        left: '-120%'
                    }, 1500, 'easeOutExpo');
                }
                if (index == '3') {
                    $('.section3').find('p').delay(500).animate({
                        bottom: '-120%'
                    }, 1500, 'easeOutExpo');
                }
                if (index == '4') {
                    $('.section4').find('p').fadeOut(2000);
                }
            }

        });
        $(".firstSesion").css("height", $(window).height() - 100 + "px");
        $(".yanProduc li").click(function () {
            // $(".coverPage").show();
        })
        $(".close").click(function () {
            // $(".coverPage").hide();
        })
    </script>


</body>

</html>