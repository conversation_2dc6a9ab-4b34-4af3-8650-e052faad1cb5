<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>鲲鹏资产配置助手 - 数投科技</title>
    <meta http-equiv="keywords" content="鲲鹏资产配置助手,智能资产配置,理财经理工具,数投科技,财富管理" />
    <meta http-equiv="description" content="鲲鹏智能资产配置助手小程序，专为银行理财经理设计的智能化财富管理工具，提供客户财富诊断、资产配置流程和调仓报告生成等服务。" />
    <meta name="Resource-type" content="Document" />

    <link rel="stylesheet" type="text/css" href="../style/coumon.css" />
    <style>
        body {
            font-family: "Microsoft YaHei", sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }

        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
            text-align: center;
        }

        .hero-content h1 {
            font-size: 48px;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .hero-content p {
            font-size: 24px;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .product-overview {
            padding: 80px 0;
            background: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .section-title {
            text-align: center;
            font-size: 36px;
            color: #333;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .section-subtitle {
            text-align: center;
            font-size: 18px;
            color: #666;
            margin-bottom: 60px;
            line-height: 1.6;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            margin-bottom: 80px;
        }

        .feature-card {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-left: 5px solid #B48B4E;
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            font-size: 24px;
            color: white;
        }

        .feature-card h3 {
            font-size: 24px;
            color: #333;
            margin-bottom: 15px;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
            font-size: 16px;
        }

        .phone-mockup-section {
            background: #f8f9fa;
            padding: 80px 0;
        }

        .phone-mockup {
            display: flex;
            align-items: center;
            gap: 60px;
        }

        .phone-images {
            flex: 1;
            display: flex;
            gap: 20px;
            justify-content: center;
        }

        .phone-screen {
            width: 350px;
            height: auto;
            background: transparent;
            border-radius: 0;
            padding: 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            overflow: hidden;
        }

        .phone-screen img {
            width: 100%;
            height: auto;
            object-fit: contain;
            border-radius: 0;
            display: block;
        }

        .phone-content {
            flex: 1;
        }

        .phone-content h2 {
            font-size: 32px;
            color: #333;
            margin-bottom: 30px;
        }

        .phone-content p {
            font-size: 18px;
            color: #666;
            line-height: 1.8;
            margin-bottom: 20px;
        }

        .qr-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 80px 0;
            text-align: center;
        }

        .qr-container {
            background: white;
            border-radius: 20px;
            padding: 60px 40px;
            display: inline-block;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .qr-container h2 {
            font-size: 32px;
            color: #333;
            margin-bottom: 20px;
        }

        .qr-container p {
            font-size: 18px;
            color: #666;
            margin-bottom: 40px;
        }

        .qr-code {
            width: 200px;
            height: 200px;
            margin: 0 auto 20px;
            border: 2px solid #eee;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            overflow: hidden;
        }

        .qr-code img {
            width: 180px;
            height: 180px;
            object-fit: contain;
            display: block;
        }

        .qr-text {
            color: #888;
            font-size: 16px;
        }

        .stats-section {
            background: white;
            padding: 60px 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 40px;
            text-align: center;
        }

        .stat-item h3 {
            font-size: 48px;
            color: #B48B4E;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .stat-item p {
            color: #666;
            font-size: 16px;
        }

        @media (max-width: 768px) {
            .hero-content h1 {
                font-size: 36px;
            }
            .hero-content p {
                font-size: 18px;
            }
            .phone-mockup {
                flex-direction: column;
            }
            .phone-images {
                order: 2;
            }
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>

<body>
    <div class="header">
        <ul>
            <li>
                <a href="aboutYushi.html">关于数投科技</a>
            </li>
            <li>
                <a>专业机构服务</a>
                <div class="second-level">
                    <a href="javascript:void(0)" id="toKunpeng">鲲鹏财富管理</a>
                    <a href="AItouYan.html">AI投研</a>
                    <a href="kunpengAssetAllocation.html">鲲鹏资产配置助手</a>
                    <a href="shumunlessBase.html">数投无人基</a>
                    <a href="javascript:void(0)" id="toljs">炼金术系统</a>
                </div>
            </li>
            <li class="middle-logo">
                <a href="../index.html"></a>
            </li>
            <li>
                <a href="touLaboratory.html">智能投顾实验室</a>
            </li>
            <li>
                <a href="inforYushi.html">数投科技动态</a>
            </li>
            <li>
                <a href="joinUs.html">加入我们</a>
            </li>
        </ul>
    </div>

    <div class="hero-section">
        <div class="hero-content">
            <h1>鲲鹏产品方案</h1>
            <p>依托鲲鹏核心系统，提供整套产品解决方案</p>
        </div>
    </div>

    <div class="product-overview">
        <div class="container">
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">💎</div>
                    <h3>数字化智能系统</h3>
                    <p>专为银行理财经理设计的智能化财富管理工具——鲲鹏智能资产配置助手，将控制化资产配置投资计划和研究工具服务应用到不同的功能场景，提供全方位的财富管理服务赋能</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🔒</div>
                    <h3>服务范式</h3>
                    <p>以资产配置为核心的从获客、管户、客约、资配、到定期调仓的财富管理五步法服务范式，加速银行财富管理服务的转型</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>投资计划（根据项目专属定制）</h3>
                    <p>依托数投的综合技术智能投资大模型，为不同层级的客户量身定制了多样化的投资计划，无论是长尾客户、财富客户、还是私行客户，系统都能精准匹配其投资需求，同时满足银行产品销售的需求，实现个性化资产配置</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">✈️</div>
                    <h3>AI培训及理财经理人工服务</h3>
                    <p>针对理财经理综合专项培训及日常售后服务支持，持续提升理财经理对于财富端客户的管理及服务能力</p>
                </div>
            </div>
        </div>
    </div>

    <div class="phone-mockup-section">
        <div class="container">
            <div class="section-title">数字化系统-鲲鹏助手小程序</div>
            <div class="section-subtitle">
                鲲鹏智能资产配置助手小程序满足理财经理在不同客户类型及多样化场景下的财富管理服务需求，提供包括获客工具、客户财富诊断、个性化资产配置、智能调仓建议等内的全方位服务，依托先进的数据分析与AI技术，助力理财经理精准洞察客户需求，优化投资策略。通过賦能理财经理专业能力，为银行财富管理数字化转型提供有力支持，全面提升服务竞争力与客户满意度。
            </div>
            
            <div class="phone-mockup">
                <div class="phone-content">
                    <h2>智能助手功能</h2>
                    <p><strong>• 客户财富诊断：</strong>三秒内完成客户风险评估和财富状况分析</p>
                    <p><strong>• 个性化配置：</strong>三分钟内生成定制化资产配置报告</p>
                    <p><strong>• 实时调仓：</strong>根据市场变化实时生成调仓建议和提醒</p>
                    <p><strong>• 投资计划：</strong>多样化投资方案，满足不同客户需求</p>
                    <p><strong>• 专业培训：</strong>理财经理专项培训和售后服务支持</p>
                </div>
                
                <div class="phone-images">
                    <div class="phone-screen">
                        <img src="../images/屏幕截图 2025-07-22 132428.png" 
                             alt="鲲鹏助手小程序界面" 
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" 
                             style="width: 100%; height: 100%; object-fit: contain; border-radius: 20px;" />
                        <div style="display: none; width: 100%; height: 100%; background: linear-gradient(to bottom, #4CAF50, #45a049); border-radius: 20px; align-items: center; justify-content: center; color: white; font-size: 18px; text-align: center; flex-direction: column;">
                            <div>鲲鹏助手小程序界面</div>
                            <div style="font-size: 14px; opacity: 0.8; margin-top: 20px;">
                                • 客户管理<br/>
                                • 资产配置<br/>
                                • 投资计划<br/>
                                • 风险评估
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="stats-section">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item">
                    <h3>3秒</h3>
                    <p>客户财富诊断</p>
                </div>
                <div class="stat-item">
                    <h3>3分钟</h3>
                    <p>资产配置方案</p>
                </div>
                <div class="stat-item">
                    <h3>5+2</h3>
                    <p>服务银行数量</p>
                </div>
                <div class="stat-item">
                    <h3>24/7</h3>
                    <p>智能服务支持</p>
                </div>
            </div>
        </div>
    </div>

    <div class="qr-section">
        <div class="container">
            <div class="qr-container">
                <h2>立即体验鲲鹏助手</h2>
                <p>扫描下方二维码，体验智能资产配置助手小程序</p>
                <div class="qr-code">
                    <img src="../images/微信图片_20250722132544_472.jpg" 
                         alt="鲲鹏助手小程序二维码" 
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                         style="width: 180px; height: 180px; object-fit: contain;" />
                    <div style="display: none; width: 180px; height: 180px; background: #f0f0f0; border-radius: 10px; align-items: center; justify-content: center; color: #999; font-size: 14px; text-align: center; flex-direction: column;">
                        <div>微信小程序二维码</div>
                        <div style="font-size: 12px; margin-top: 10px;">扫码体验鲲鹏助手</div>
                    </div>
                </div>
                <p class="qr-text">微信扫一扫，立即体验智能理财服务</p>
            </div>
        </div>
    </div>

</body>

</html>
