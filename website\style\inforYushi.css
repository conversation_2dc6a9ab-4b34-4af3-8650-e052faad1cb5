.section{
    text-align:center;
}


/* Backgrounds will cover all the section
* --------------------------------------- */
.section{
            background-size: cover;
        }
        .slide{
            background-size: cover;
        }
        #toljs{
            font-size: 14px;
        }

.firstSesion{
    padding-top:100px;
    width: 100%;
    background-image: url(../images/dongtai.jpg);
    /*background-size: 100% 100%;*/
    background-size: cover;
}
.wordContent ul{
    display: flex;
    flex-direction: row;
    flex-wrap:nowrap;
    justify-content:space-between;
    width: 700px;
    margin:50px auto;

}
.wordContent ul li{
    padding:10px;
    color:#000;
    font-size: 30px;
    font-weight: 700;
}
.wordContent ul .active{
    color:#CBAE60;
    border-bottom: 3px solid #CBAE60;
}
.information{
    width: 1150px;
    margin:0 auto;
}
.information ul{

}
.information ul li{
    width: 1150px;
    height: 200px;
    border-bottom: 1px solid #CCC;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
}
.information ul li:first-child{
    border-top: 1px solid #CCC;
}
.information ul li .dayTime,.dayTitle .dayTime{
    width: 120px;
    height: 120px;
    border: 1px solid #CCC;
    margin-top:40px;
}
.dayTime .dayNo{
    font-size: 56px;
    color:#000;
    margin: 12px 0 8px;
}
.information ul li .inforMsg{
    width: 976px;
}
.inforMsg .inforHead{
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    width: 976px;
    margin: 25px 0;
}
.inforMsg .inforHead .inforTitle{
    font-size: 26px;
    color: #000;
}
.inforMsg .inforHead .inforHref{
    color: #3D96DF;
    font-size: 18px;
}
.inforHref a:hover,.inforHref a:hover,.inforHref a:link,.inforHref a:VISITED,.inforHref a:active{
    color: #3D96DF;
}
.inforMsg .inforDetail{
    font-size: 16px;
    word-break: break-all;
    width: 976px;
    text-align: left;
    line-height: 24px;
}
.inforPage{
    width: 800px;
    margin: 40px auto;
}
.inforPage ul{
    width: 800px;
    margin: 0 auto;
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
}
.inforPage ul li{
    margin-left: 2px;
}
.inforPage ul li a{
    display: block;
    width: 40px;
    height: 40px;
    /*border:1px solid #CCC;*/
    background-color: #EEE;
    line-height: 40px;
}
.inforPage ul li .active{
    background-color: #CBAE60;
}
.inforDetMsg{
    width: 1150px;
    position: relative;
    margin: 0 auto;
}
.dayTitle{
    position: absolute;
    left:-130px;
    top:17px;
}
.dayMsgName{
    width: 120px;
    height: 38px;
    border: 1px solid #CCC;
    border-top:none;
    background-color: #EEE;
    line-height: 36px;
}
.breadList{
    text-align: left;
    font-size: 14px;
    padding-top:15px;
}
.breadList a{
    margin-right: 5px;
}
.breadList a:last-child{
    color: #000000;
}
.infoTitle{
    font-size: 26px;
    margin-top:25px;
    color:#000;
}
.inforSub{
    font-size: 20px;
    margin: 15px 0 40px;
}
.infoNews{
    width: 1149px;
    font-size: 16px;
    word-break: break-all;
    padding-bottom: 200px;
    text-align: left;
    line-height: 24px;
}
.inforLast,.inforNext{
    width: 300px;
    height: 63px;
    position: absolute;
    bottom: 0;
    border-left: 3px solid #CCC;
    background-color: #EEE;
    padding:20px;
    text-align: left;
    z-index: 1000;
}
.inforNext{
    right:0
}
.inforLast{
    left: 0;
}
.hrefHead{
    font-size: 14px;
    margin-bottom: 8px;
}
.hrefTitle{
    color: #000;
    overflow: hidden;
    white-space: nowrap;
    text-overflow:ellipsis;
}

