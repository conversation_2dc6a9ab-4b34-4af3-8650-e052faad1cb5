apiVersion: v1
kind: Service
metadata:
  name: website-service
  namespace: aigu-product
spec:
#  type: LoadBalancer
  type: NodePort
  ports:
  - port: 80
    targetPort: 80
  selector:
    app: website-deployment
  
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: website-deployment
  namespace: aigu-product
  labels:
    web: website-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: website-deployment
  template:
    metadata:
      labels:
        app: website-deployment
    spec:
      nodeSelector:
        env: aigu-product
      containers:
      - name: guanwang-website
        image: ${IMAGE_TAG}
        imagePullPolicy: Always
        ports:
        - containerPort: 80
        resources:            
          limits:
            cpu: "512m"
            memory: "1024Mi"
          requests:
            cpu: "256m"
            memory: "512Mi"  
        env:
          - name: SPRING_PROFILES_ACTIVE
            value: "product"      
          - name: time
            value: "1"
        volumeMounts:
          - name: nas-aigu-product-website-springboot-pvc
            mountPath: "/var/log/nginx"
      imagePullSecrets:          
      - name: myregistrykey            

      volumes:
      - name: nas-aigu-product-website-springboot-pvc
        persistentVolumeClaim:
          claimName: nas-aigu-product-website-springboot-pvc

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: nas-aigu-product-website-springboot-pvc
  namespace: aigu-product
  annotations:
    volume.beta.kubernetes.io/storage-class: "alicloud-nas-product"
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: alicloud-nas-product
  resources:
    requests:
      storage: 5Gi

---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: website
  namespace: aigu-product
spec:
  tls:
  - hosts:
    - www.51lianjin.com
    secretName: aigu-secret
  rules:
  - host: www.51lianjin.com
    http:
      paths:
      - backend:
          serviceName: website-service
          servicePort: 80
        path: /